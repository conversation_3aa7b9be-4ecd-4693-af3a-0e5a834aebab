#ifndef EVALUATE_H
#define EVALUATE_H

#include "types.h"

// Forward declaration
class Position;

// Main evaluation function
Value evaluate(const Position& pos);

// Evaluation constants
constexpr Value Tempo = 28;

// Piece-square tables
extern Value PieceSquareTable[PIECE_NB][64];

// Evaluation terms
struct EvalInfo {
    Bitboard mobilityArea[2];
    Bitboard attackedBy[2][PIECE_TYPE_NB];
    Bitboard attackedBy2[2];
    Bitboard kingRing[2];
    Bitboard kingAttackersCount[2];
    Bitboard kingAttackersWeight[2];
    Bitboard kingAdjacentZoneAttacksCount[2];
    int kingAttacksCount[2];
};

// Evaluation weights
constexpr Value MobilityBonus[][32] = {
    {}, {}, // Pawns, Kings (not used)
    { -75,-57,-9,-2,6,14,22,29,36 }, // Knights
    { -48,-20,16,26,38,51,55,63,63,68,81,81,91,98 }, // Bishops
    { -58,-27,-15,-10,-5,-2,9,16,30,29,32,38,46,48,48 }, // Rooks
    { -39,-21,3,3,14,22,28,41,43,48,56,60,60,66,67,70,71,73,79,88,88,99,102,102,106,109,113,116,117,118,119,120 } // Queens
};

// King safety evaluation
constexpr Value KingDanger[512] = {
    0, 0, 1, 2, 3, 5, 7, 9, 12, 15,
    18, 22, 26, 30, 35, 39, 44, 50, 56, 62,
    68, 75, 82, 85, 89, 97, 105, 113, 122, 131,
    140, 150, 169, 180, 191, 202, 213, 225, 237, 248,
    260, 272, 283, 295, 307, 319, 330, 342, 354, 366,
    377, 389, 401, 412, 424, 436, 448, 459, 471, 483,
    494, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500, 500, 500, 500, 500, 500, 500, 500, 500,
    500, 500
};

// Evaluation phases
enum Phase {
    PHASE_ENDGAME = 0,
    PHASE_MIDGAME = 128,
    MG = 0, EG = 1, PHASE_NB = 2
};

// Score type for storing midgame and endgame values
typedef int Score;

constexpr Score make_score(int mg, int eg) {
    return Score((int)((unsigned int)eg << 16) + mg);
}

constexpr Value mg_value(Score s) {
    return Value(((s + 0x8000) & ~0xffff) / 0x10000);
}

constexpr Value eg_value(Score s) {
    return Value(int16_t(s & 0xffff));
}

// Evaluation helper functions
template<Color Us>
Value evaluate_pieces(const Position& pos, EvalInfo& ei, Score& score);

template<Color Us>
Value evaluate_king(const Position& pos, EvalInfo& ei, Score& score);

template<Color Us>
Value evaluate_threats(const Position& pos, EvalInfo& ei, Score& score);

template<Color Us>
Value evaluate_passed_pawns(const Position& pos, EvalInfo& ei, Score& score);

// Initialize evaluation tables
void init_eval();

// Trace evaluation for debugging
std::string trace(const Position& pos);

#endif // EVALUATE_H

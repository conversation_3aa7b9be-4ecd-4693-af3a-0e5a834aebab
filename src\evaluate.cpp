#include "evaluate.h"
#include "position.h"
#include "bitboard.h"
#include <algorithm>
#include <sstream>

// Piece-square tables
Value PieceSquareTable[PIECE_NB][64];

namespace {
    // Piece-square table values (middlegame, endgame)
    constexpr Score PawnTable[64] = {
        make_score(  0,  0), make_score(  0,  0), make_score(  0,  0), make_score(  0,  0),
        make_score(  0,  0), make_score(  0,  0), make_score(  0,  0), make_score(  0,  0),
        make_score(-11, 13), make_score(  6,-25), make_score( -7, -1), make_score(  4, -7),
        make_score(  4, -7), make_score( -7, -1), make_score(  6,-25), make_score(-11, 13),
        make_score(-18, -4), make_score(-18, -4), make_score(-10,  2), make_score( 15,  8),
        make_score( 15,  8), make_score(-10,  2), make_score(-18, -4), make_score(-18, -4),
        make_score(-17,  3), make_score(-15,  9), make_score(-8,  19), make_score( 13, 16),
        make_score( 13, 16), make_score(-8,  19), make_score(-15,  9), make_score(-17,  3),
        make_score(-6,   9), make_score(-8,   8), make_score( 2,  17), make_score( 23, 15),
        make_score( 23, 15), make_score( 2,  17), make_score(-8,   8), make_score(-6,   9),
        make_score( 7,  11), make_score( 3,  13), make_score( 3,  19), make_score( 15, 21),
        make_score( 15, 21), make_score( 3,  19), make_score( 3,  13), make_score( 7,  11),
        make_score( 11, 20), make_score(-3,  23), make_score(-7,  23), make_score( 12, 35),
        make_score( 12, 35), make_score(-7,  23), make_score(-3,  23), make_score( 11, 20),
        make_score(  0,  0), make_score(  0,  0), make_score(  0,  0), make_score(  0,  0),
        make_score(  0,  0), make_score(  0,  0), make_score(  0,  0), make_score(  0,  0)
    };

    constexpr Score KnightTable[64] = {
        make_score(-143, -97), make_score(-96, -82), make_score(-80, -46), make_score(-73, -14),
        make_score(-73, -14), make_score(-80, -46), make_score(-96, -82), make_score(-143, -97),
        make_score(-83, -69), make_score(-43, -55), make_score(-21, -17), make_score(-10,   9),
        make_score(-10,   9), make_score(-21, -17), make_score(-43, -55), make_score(-83, -69),
        make_score(-71, -50), make_score(-22, -39), make_score(  0, -7), make_score(  9,  28),
        make_score(  9,  28), make_score(  0, -7), make_score(-22, -39), make_score(-71, -50),
        make_score(-25, -41), make_score( 18, -25), make_score( 43,   6), make_score( 47,  38),
        make_score( 47,  38), make_score( 43,   6), make_score( 18, -25), make_score(-25, -41),
        make_score(-26, -46), make_score( 16, -25), make_score( 38,   3), make_score( 50,  40),
        make_score( 50,  40), make_score( 38,   3), make_score( 16, -25), make_score(-26, -46),
        make_score(-11, -55), make_score( 37, -38), make_score( 56, -8), make_score( 71,  27),
        make_score( 71,  27), make_score( 56, -8), make_score( 37, -38), make_score(-11, -55),
        make_score(-62, -64), make_score(-17, -50), make_score(  5, -24), make_score( 14,  13),
        make_score( 14,  13), make_score(  5, -24), make_score(-17, -50), make_score(-62, -64),
        make_score(-195, -110), make_score(-66, -90), make_score(-42, -50), make_score(-29, -13),
        make_score(-29, -13), make_score(-42, -50), make_score(-66, -90), make_score(-195, -110)
    };

    constexpr Score BishopTable[64] = {
        make_score(-54, -68), make_score(-27, -21), make_score(-34, -26), make_score(-43,  -6),
        make_score(-43,  -6), make_score(-34, -26), make_score(-27, -21), make_score(-54, -68),
        make_score(-29, -21), make_score( 10, -26), make_score(-3,  -6), make_score(-27, -20),
        make_score(-27, -20), make_score(-3,  -6), make_score( 10, -26), make_score(-29, -21),
        make_score(-19, -26), make_score( 17,  -6), make_score( 11, -20), make_score(  1, -17),
        make_score(  1, -17), make_score( 11, -20), make_score( 17,  -6), make_score(-19, -26),
        make_score(-13,  -6), make_score( 13, -20), make_score(  5, -17), make_score(-4, -21),
        make_score(-4, -21), make_score(  5, -17), make_score( 13, -20), make_score(-13,  -6),
        make_score(-5, -20), make_score( 15, -17), make_score(  8, -21), make_score(  4, -5),
        make_score(  4, -5), make_score(  8, -21), make_score( 15, -17), make_score(-5, -20),
        make_score(-7, -17), make_score( 22, -21), make_score( 13,  -5), make_score( 10, -4),
        make_score( 10, -4), make_score( 13,  -5), make_score( 22, -21), make_score(-7, -17),
        make_score(-21, -21), make_score(-7,  -5), make_score( -1,  -4), make_score( -5, -21),
        make_score(-5, -21), make_score( -1,  -4), make_score(-7,  -5), make_score(-21, -21),
        make_score(-48, -68), make_score(-21, -21), make_score(-19, -26), make_score(-13,  -6),
        make_score(-13,  -6), make_score(-19, -26), make_score(-21, -21), make_score(-48, -68)
    };

    constexpr Score RookTable[64] = {
        make_score(-25, -30), make_score(-16, -12), make_score(-16,  -8), make_score(-9,  -5),
        make_score(-9,  -5), make_score(-16,  -8), make_score(-16, -12), make_score(-25, -30),
        make_score(-21, -36), make_score(-8, -19), make_score(-3,  -15), make_score( 2, -8),
        make_score( 2, -8), make_score(-3,  -15), make_score(-8, -19), make_score(-21, -36),
        make_score(-21, -36), make_score(-9, -19), make_score(-4,  -15), make_score( 2, -8),
        make_score( 2, -8), make_score(-4,  -15), make_score(-9, -19), make_score(-21, -36),
        make_score(-22, -36), make_score(-6, -19), make_score(-1,  -15), make_score( 2, -8),
        make_score( 2, -8), make_score(-1,  -15), make_score(-6, -19), make_score(-22, -36),
        make_score(-22, -36), make_score(-7, -19), make_score( 0,  -15), make_score( 1, -8),
        make_score( 1, -8), make_score( 0,  -15), make_score(-7, -19), make_score(-22, -36),
        make_score(-21, -36), make_score(-7, -19), make_score( 0,  -15), make_score( 2, -8),
        make_score( 2, -8), make_score( 0,  -15), make_score(-7, -19), make_score(-21, -36),
        make_score(-12, -36), make_score( 4, -19), make_score( 8,  -15), make_score(12, -8),
        make_score(12, -8), make_score( 8,  -15), make_score( 4, -19), make_score(-12, -36),
        make_score(-23, -30), make_score(-15, -12), make_score(-11,  -8), make_score(-5, -5),
        make_score(-5, -5), make_score(-11,  -8), make_score(-15, -12), make_score(-23, -30)
    };

    constexpr Score QueenTable[64] = {
        make_score( 0, -71), make_score(-4, -56), make_score(-3, -42), make_score(-1, -29),
        make_score(-1, -29), make_score(-3, -42), make_score(-4, -56), make_score( 0, -71),
        make_score(-4, -56), make_score( 6, -30), make_score( 9, -21), make_score( 8, -5),
        make_score( 8, -5), make_score( 9, -21), make_score( 6, -30), make_score(-4, -56),
        make_score(-2, -42), make_score( 6, -21), make_score( 1,  -8), make_score(-3,  6),
        make_score(-3,  6), make_score( 1,  -8), make_score( 6, -21), make_score(-2, -42),
        make_score(-1, -29), make_score( 8,  -5), make_score( 1,   6), make_score( 4, 21),
        make_score( 4, 21), make_score( 1,   6), make_score( 8,  -5), make_score(-1, -29),
        make_score(-4, -29), make_score( 6,  -5), make_score( 1,   6), make_score( 3, 21),
        make_score( 3, 21), make_score( 1,   6), make_score( 6,  -5), make_score(-4, -29),
        make_score(-1, -42), make_score( 6, -21), make_score( 2,  -8), make_score( 3,  6),
        make_score( 3,  6), make_score( 2,  -8), make_score( 6, -21), make_score(-1, -42),
        make_score(-1, -56), make_score( 8, -30), make_score( 8, -21), make_score( 6, -5),
        make_score( 6, -5), make_score( 8, -21), make_score( 8, -30), make_score(-1, -56),
        make_score(-2, -71), make_score( 6, -56), make_score( 4, -42), make_score( 2, -29),
        make_score( 2, -29), make_score( 4, -42), make_score( 6, -56), make_score(-2, -71)
    };

    constexpr Score KingTable[64] = {
        make_score(267,  0), make_score(320, 41), make_score(270, 80), make_score(195, 93),
        make_score(195, 93), make_score(270, 80), make_score(320, 41), make_score(267,  0),
        make_score(264, 41), make_score(304, 56), make_score(238, 80), make_score(180, 78),
        make_score(180, 78), make_score(238, 80), make_score(304, 56), make_score(264, 41),
        make_score(200, 80), make_score(220, 80), make_score(176, 79), make_score(142, 87),
        make_score(142, 87), make_score(176, 79), make_score(220, 80), make_score(200, 80),
        make_score(175, 93), make_score(180, 78), make_score(140, 87), make_score(105, 99),
        make_score(105, 99), make_score(140, 87), make_score(180, 78), make_score(175, 93),
        make_score(164, 93), make_score(190, 78), make_score(138, 87), make_score(107, 99),
        make_score(107, 99), make_score(138, 87), make_score(190, 78), make_score(164, 93),
        make_score(154, 80), make_score(179, 80), make_score(105, 79), make_score( 70, 87),
        make_score( 70, 87), make_score(105, 79), make_score(179, 80), make_score(154, 80),
        make_score(123, 41), make_score(145, 56), make_score( 81, 80), make_score( 31, 78),
        make_score( 31, 78), make_score( 81, 80), make_score(145, 56), make_score(123, 41),
        make_score( 88,  0), make_score(120, 41), make_score( 65, 80), make_score( 33, 93),
        make_score( 33, 93), make_score( 65, 80), make_score(120, 41), make_score( 88,  0)
    };

    // Initialize piece-square tables
    void init_psqt() {
        for (Square s = SQ_A1; s <= SQ_H8; ++s) {
            PieceSquareTable[W_PAWN][s] = mg_value(PawnTable[s]);
            PieceSquareTable[B_PAWN][s ^ 56] = mg_value(PawnTable[s]);
            
            PieceSquareTable[W_KNIGHT][s] = mg_value(KnightTable[s]);
            PieceSquareTable[B_KNIGHT][s ^ 56] = mg_value(KnightTable[s]);
            
            PieceSquareTable[W_BISHOP][s] = mg_value(BishopTable[s]);
            PieceSquareTable[B_BISHOP][s ^ 56] = mg_value(BishopTable[s]);
            
            PieceSquareTable[W_ROOK][s] = mg_value(RookTable[s]);
            PieceSquareTable[B_ROOK][s ^ 56] = mg_value(RookTable[s]);
            
            PieceSquareTable[W_QUEEN][s] = mg_value(QueenTable[s]);
            PieceSquareTable[B_QUEEN][s ^ 56] = mg_value(QueenTable[s]);
            
            PieceSquareTable[W_KING][s] = mg_value(KingTable[s]);
            PieceSquareTable[B_KING][s ^ 56] = mg_value(KingTable[s]);
        }
    }

    // Compute game phase based on material
    Phase game_phase(const Position& pos) {
        Value npm = pos.non_pawn_material();
        npm = std::max(npm, Value(PHASE_ENDGAME));
        npm = std::min(npm, Value(PHASE_MIDGAME));
        return Phase(((npm - PHASE_ENDGAME) * PHASE_MIDGAME) / (PHASE_MIDGAME - PHASE_ENDGAME));
    }

    // Interpolate between midgame and endgame values
    Value interpolate(Score s, Phase ph) {
        int mg = mg_value(s);
        int eg = eg_value(s);
        return Value((mg * ph + eg * (PHASE_MIDGAME - ph)) / PHASE_MIDGAME);
    }
}

void init_eval() {
    init_psqt();
}

Value evaluate(const Position& pos) {
    // Quick draw detection
    if (pos.non_pawn_material() == VALUE_ZERO)
        return VALUE_DRAW;

    Color us = pos.side_to_move();
    
    // Material evaluation
    Value material = pos.non_pawn_material(WHITE) - pos.non_pawn_material(BLACK);
    
    // Add pawn material
    material += (pos.count<PAWN>(WHITE) - pos.count<PAWN>(BLACK)) * PIECE_VALUES[PAWN];
    
    // Piece-square table evaluation
    Value psqt = VALUE_ZERO;
    for (Square s = SQ_A1; s <= SQ_H8; ++s) {
        Piece pc = pos.piece_on(s);
        if (pc != NO_PIECE) {
            if (color_of(pc) == WHITE)
                psqt += PieceSquareTable[pc][s];
            else
                psqt -= PieceSquareTable[pc][s];
        }
    }
    
    // Simple mobility evaluation
    Value mobility = VALUE_ZERO;
    
    // Knight mobility
    Bitboard knights = pos.pieces(WHITE, KNIGHT);
    while (knights) {
        Square s = pop_lsb(knights);
        mobility += popcount(attacks_bb(KNIGHT, s) & ~pos.pieces(WHITE)) * 4;
    }
    
    knights = pos.pieces(BLACK, KNIGHT);
    while (knights) {
        Square s = pop_lsb(knights);
        mobility -= popcount(attacks_bb(KNIGHT, s) & ~pos.pieces(BLACK)) * 4;
    }
    
    // Bishop mobility
    Bitboard bishops = pos.pieces(WHITE, BISHOP);
    while (bishops) {
        Square s = pop_lsb(bishops);
        mobility += popcount(attacks_bb(BISHOP, s, pos.pieces()) & ~pos.pieces(WHITE)) * 3;
    }
    
    bishops = pos.pieces(BLACK, BISHOP);
    while (bishops) {
        Square s = pop_lsb(bishops);
        mobility -= popcount(attacks_bb(BISHOP, s, pos.pieces()) & ~pos.pieces(BLACK)) * 3;
    }
    
    // Rook mobility
    Bitboard rooks = pos.pieces(WHITE, ROOK);
    while (rooks) {
        Square s = pop_lsb(rooks);
        mobility += popcount(attacks_bb(ROOK, s, pos.pieces()) & ~pos.pieces(WHITE)) * 2;
    }
    
    rooks = pos.pieces(BLACK, ROOK);
    while (rooks) {
        Square s = pop_lsb(rooks);
        mobility -= popcount(attacks_bb(ROOK, s, pos.pieces()) & ~pos.pieces(BLACK)) * 2;
    }
    
    // Queen mobility
    Bitboard queens = pos.pieces(WHITE, QUEEN);
    while (queens) {
        Square s = pop_lsb(queens);
        mobility += popcount(attacks_bb(QUEEN, s, pos.pieces()) & ~pos.pieces(WHITE)) * 1;
    }
    
    queens = pos.pieces(BLACK, QUEEN);
    while (queens) {
        Square s = pop_lsb(queens);
        mobility -= popcount(attacks_bb(QUEEN, s, pos.pieces()) & ~pos.pieces(BLACK)) * 1;
    }
    
    Value eval = material + psqt + mobility;
    
    // Add tempo bonus
    eval += (us == WHITE ? Tempo : -Tempo);
    
    return us == WHITE ? eval : -eval;
}

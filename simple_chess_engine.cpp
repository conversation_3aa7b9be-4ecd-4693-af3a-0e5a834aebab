#include <iostream>
#include <vector>
#include <string>
#include <algorithm>
#include <sstream>

// Simple Chess Engine - Single File Version
// This is a simplified version that can be compiled easily

using namespace std;

// Basic types
typedef uint64_t Bitboard;
typedef int Square;
typedef int Piece;
typedef int Color;
typedef uint32_t Move;
typedef int Value;

// Constants
const Color WHITE = 0, BLACK = 1;
const Piece EMPTY = 0, PAWN = 1, KNIGHT = 2, BISHOP = 3, ROOK = 4, QUEEN = 5, KING = 6;
const Value PIECE_VALUES[] = {0, 100, 320, 330, 500, 900, 20000};

// Simple board representation
class SimpleBoard {
public:
    int board[64];
    Color to_move;
    
    SimpleBoard() {
        // Initialize starting position
        for (int i = 0; i < 64; i++) board[i] = EMPTY;
        
        // White pieces
        board[0] = board[7] = ROOK;
        board[1] = board[6] = KNIGHT;
        board[2] = board[5] = BISHOP;
        board[3] = QUEEN;
        board[4] = KING;
        for (int i = 8; i < 16; i++) board[i] = PAWN;
        
        // Black pieces
        board[56] = board[63] = -ROOK;
        board[57] = board[62] = -KNIGHT;
        board[58] = board[61] = -BISHOP;
        board[59] = -QUEEN;
        board[60] = -KING;
        for (int i = 48; i < 56; i++) board[i] = -PAWN;
        
        to_move = WHITE;
    }
    
    void print() {
        cout << "\n  a b c d e f g h\n";
        for (int rank = 7; rank >= 0; rank--) {
            cout << (rank + 1) << " ";
            for (int file = 0; file < 8; file++) {
                int sq = rank * 8 + file;
                char piece_char = piece_to_char(board[sq]);
                cout << piece_char << " ";
            }
            cout << (rank + 1) << "\n";
        }
        cout << "  a b c d e f g h\n";
        cout << "Turn: " << (to_move == WHITE ? "White" : "Black") << "\n\n";
    }
    
    char piece_to_char(int piece) {
        const string pieces = ".PNBRQKpnbrqk";
        if (piece == 0) return '.';
        if (piece > 0) return pieces[piece];
        return pieces[6 - piece];
    }
    
    bool is_valid_square(int sq) {
        return sq >= 0 && sq < 64;
    }
    
    vector<Move> generate_moves() {
        vector<Move> moves;
        
        for (int from = 0; from < 64; from++) {
            int piece = board[from];
            if (piece == 0) continue;
            if ((piece > 0) != (to_move == WHITE)) continue;
            
            int piece_type = abs(piece);
            
            switch (piece_type) {
                case PAWN:
                    generate_pawn_moves(from, moves);
                    break;
                case KNIGHT:
                    generate_knight_moves(from, moves);
                    break;
                case BISHOP:
                    generate_bishop_moves(from, moves);
                    break;
                case ROOK:
                    generate_rook_moves(from, moves);
                    break;
                case QUEEN:
                    generate_queen_moves(from, moves);
                    break;
                case KING:
                    generate_king_moves(from, moves);
                    break;
            }
        }
        
        return moves;
    }
    
    void generate_pawn_moves(int from, vector<Move>& moves) {
        int direction = (to_move == WHITE) ? 8 : -8;
        int start_rank = (to_move == WHITE) ? 1 : 6;
        
        // Forward move
        int to = from + direction;
        if (is_valid_square(to) && board[to] == 0) {
            moves.push_back(make_move(from, to));
            
            // Double move from starting position
            if (from / 8 == start_rank) {
                to = from + 2 * direction;
                if (is_valid_square(to) && board[to] == 0) {
                    moves.push_back(make_move(from, to));
                }
            }
        }
        
        // Captures
        int left_capture = from + direction - 1;
        int right_capture = from + direction + 1;
        
        if (is_valid_square(left_capture) && (from % 8) > 0 && 
            board[left_capture] != 0 && ((board[left_capture] > 0) != (to_move == WHITE))) {
            moves.push_back(make_move(from, left_capture));
        }
        
        if (is_valid_square(right_capture) && (from % 8) < 7 && 
            board[right_capture] != 0 && ((board[right_capture] > 0) != (to_move == WHITE))) {
            moves.push_back(make_move(from, right_capture));
        }
    }
    
    void generate_knight_moves(int from, vector<Move>& moves) {
        int knight_moves[] = {-17, -15, -10, -6, 6, 10, 15, 17};
        
        for (int delta : knight_moves) {
            int to = from + delta;
            if (is_valid_square(to) && abs((from % 8) - (to % 8)) <= 2) {
                if (board[to] == 0 || ((board[to] > 0) != (to_move == WHITE))) {
                    moves.push_back(make_move(from, to));
                }
            }
        }
    }
    
    void generate_sliding_moves(int from, vector<Move>& moves, const vector<int>& directions) {
        for (int direction : directions) {
            for (int to = from + direction; is_valid_square(to); to += direction) {
                // Check if we've wrapped around the board
                if (abs((to % 8) - ((to - direction) % 8)) > 1) break;
                
                if (board[to] == 0) {
                    moves.push_back(make_move(from, to));
                } else {
                    if ((board[to] > 0) != (to_move == WHITE)) {
                        moves.push_back(make_move(from, to));
                    }
                    break;
                }
            }
        }
    }
    
    void generate_bishop_moves(int from, vector<Move>& moves) {
        generate_sliding_moves(from, moves, {-9, -7, 7, 9});
    }
    
    void generate_rook_moves(int from, vector<Move>& moves) {
        generate_sliding_moves(from, moves, {-8, -1, 1, 8});
    }
    
    void generate_queen_moves(int from, vector<Move>& moves) {
        generate_sliding_moves(from, moves, {-9, -8, -7, -1, 1, 7, 8, 9});
    }
    
    void generate_king_moves(int from, vector<Move>& moves) {
        int king_moves[] = {-9, -8, -7, -1, 1, 7, 8, 9};
        
        for (int delta : king_moves) {
            int to = from + delta;
            if (is_valid_square(to) && abs((from % 8) - (to % 8)) <= 1) {
                if (board[to] == 0 || ((board[to] > 0) != (to_move == WHITE))) {
                    moves.push_back(make_move(from, to));
                }
            }
        }
    }
    
    Move make_move(int from, int to) {
        return (from << 6) | to;
    }
    
    int from_square(Move m) { return (m >> 6) & 63; }
    int to_square(Move m) { return m & 63; }
    
    void make_move(Move m) {
        int from = from_square(m);
        int to = to_square(m);
        
        board[to] = board[from];
        board[from] = 0;
        to_move = (to_move == WHITE) ? BLACK : WHITE;
    }
    
    void unmake_move(Move m, int captured_piece) {
        int from = from_square(m);
        int to = to_square(m);
        
        board[from] = board[to];
        board[to] = captured_piece;
        to_move = (to_move == WHITE) ? BLACK : WHITE;
    }
    
    Value evaluate() {
        Value score = 0;
        
        for (int sq = 0; sq < 64; sq++) {
            int piece = board[sq];
            if (piece != 0) {
                int piece_type = abs(piece);
                int piece_value = PIECE_VALUES[piece_type];
                
                if (piece > 0) {
                    score += piece_value;
                } else {
                    score -= piece_value;
                }
            }
        }
        
        return (to_move == WHITE) ? score : -score;
    }
    
    Value minimax(int depth, Value alpha, Value beta, bool maximizing) {
        if (depth == 0) {
            return evaluate();
        }
        
        vector<Move> moves = generate_moves();
        
        if (moves.empty()) {
            return maximizing ? -30000 : 30000; // Checkmate/stalemate
        }
        
        if (maximizing) {
            Value max_eval = -50000;
            for (Move move : moves) {
                int captured = board[to_square(move)];
                make_move(move);
                Value eval = minimax(depth - 1, alpha, beta, false);
                unmake_move(move, captured);
                
                max_eval = max(max_eval, eval);
                alpha = max(alpha, eval);
                if (beta <= alpha) break;
            }
            return max_eval;
        } else {
            Value min_eval = 50000;
            for (Move move : moves) {
                int captured = board[to_square(move)];
                make_move(move);
                Value eval = minimax(depth - 1, alpha, beta, true);
                unmake_move(move, captured);
                
                min_eval = min(min_eval, eval);
                beta = min(beta, eval);
                if (beta <= alpha) break;
            }
            return min_eval;
        }
    }
    
    Move find_best_move(int depth = 4) {
        vector<Move> moves = generate_moves();
        if (moves.empty()) return 0;
        
        Move best_move = moves[0];
        Value best_score = -50000;
        
        cout << "Thinking...\n";
        
        for (Move move : moves) {
            int captured = board[to_square(move)];
            make_move(move);
            Value score = minimax(depth - 1, -50000, 50000, false);
            unmake_move(move, captured);
            
            if (score > best_score) {
                best_score = score;
                best_move = move;
            }
        }
        
        cout << "Best move score: " << best_score << "\n";
        return best_move;
    }
    
    string move_to_string(Move m) {
        int from = from_square(m);
        int to = to_square(m);
        
        string result;
        result += char('a' + (from % 8));
        result += char('1' + (from / 8));
        result += char('a' + (to % 8));
        result += char('1' + (to / 8));
        
        return result;
    }
    
    Move string_to_move(const string& move_str) {
        if (move_str.length() != 4) return 0;
        
        int from_file = move_str[0] - 'a';
        int from_rank = move_str[1] - '1';
        int to_file = move_str[2] - 'a';
        int to_rank = move_str[3] - '1';
        
        if (from_file < 0 || from_file > 7 || from_rank < 0 || from_rank > 7 ||
            to_file < 0 || to_file > 7 || to_rank < 0 || to_rank > 7) {
            return 0;
        }
        
        int from = from_rank * 8 + from_file;
        int to = to_rank * 8 + to_file;
        
        return make_move(from, to);
    }
};

int main() {
    cout << "=== Simple Chess Engine ===" << endl;
    cout << "Commands: move (e.g., e2e4), engine, quit, help" << endl;
    
    SimpleBoard board;
    string input;
    
    while (true) {
        board.print();
        cout << "Enter command: ";
        getline(cin, input);
        
        if (input == "quit" || input == "q") {
            break;
        } else if (input == "help" || input == "h") {
            cout << "Commands:\n";
            cout << "  move: e2e4 (move from e2 to e4)\n";
            cout << "  engine: let engine make a move\n";
            cout << "  quit: exit the program\n";
            cout << "  help: show this help\n\n";
        } else if (input == "engine" || input == "e") {
            Move best_move = board.find_best_move(4);
            if (best_move != 0) {
                cout << "Engine plays: " << board.move_to_string(best_move) << "\n";
                board.make_move(best_move);
            } else {
                cout << "No legal moves available!\n";
            }
        } else if (input.length() == 4) {
            Move move = board.string_to_move(input);
            if (move != 0) {
                // Check if move is legal
                vector<Move> legal_moves = board.generate_moves();
                bool is_legal = false;
                for (Move legal_move : legal_moves) {
                    if (legal_move == move) {
                        is_legal = true;
                        break;
                    }
                }
                
                if (is_legal) {
                    board.make_move(move);
                    cout << "Move played: " << input << "\n";
                } else {
                    cout << "Illegal move: " << input << "\n";
                }
            } else {
                cout << "Invalid move format. Use format like 'e2e4'\n";
            }
        } else {
            cout << "Unknown command. Type 'help' for available commands.\n";
        }
    }
    
    cout << "Thanks for playing!" << endl;
    return 0;
}

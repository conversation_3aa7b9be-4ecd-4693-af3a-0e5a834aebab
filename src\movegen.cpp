#include "movegen.h"
#include "bitboard.h"

namespace {
    template<Color Us, GenType Type>
    Move* generate_pawn_moves(const Position& pos, Move* moveList, Bitboard target) {
        constexpr Color Them = ~Us;
        constexpr Bitboard TRank8BB = (Us == WHITE ? Rank8BB : Rank1BB);
        constexpr Bitboard TRank7BB = (Us == WHITE ? Rank7BB : Rank2BB);
        constexpr Bitboard TRank3BB = (Us == WHITE ? Rank3BB : Rank6BB);
        constexpr int Up = pawn_push(Us);
        constexpr int UpRight = (Us == WHITE ? NORTH_EAST : SOUTH_WEST);
        constexpr int UpLeft = (Us == WHITE ? NORTH_WEST : SOUTH_EAST);

        const Bitboard emptySquares = ~pos.pieces();
        const Bitboard enemies = pos.pieces(Them);
        
        Bitboard pawnsOn7 = pos.pieces(Us, PAWN) & TRank7BB;
        Bitboard pawnsNotOn7 = pos.pieces(Us, PAWN) & ~TRank7BB;

        // Single and double pawn pushes
        if (Type != CAPTURES) {
            Bitboard b1 = shift<Up>(pawnsNotOn7) & emptySquares;
            Bitboard b2 = shift<Up>(b1 & TRank3BB) & emptySquares;

            if (Type == EVASIONS) {
                b1 &= target;
                b2 &= target;
            }

            if (Type == QUIET_CHECKS) {
                Square ksq = pos.square<KING>(Them);
                b1 &= pawn_attacks_bb(Them, ksq);
                b2 &= pawn_attacks_bb(Them, ksq);
            }

            while (b1) {
                Square to = pop_lsb(b1);
                *moveList++ = make_move(to - Up, to);
            }

            while (b2) {
                Square to = pop_lsb(b2);
                *moveList++ = make_move(to - Up - Up, to);
            }
        }

        // Promotions and underpromotions
        if (pawnsOn7) {
            if (Type == CAPTURES || Type == EVASIONS || Type == NON_EVASIONS) {
                Bitboard b1 = shift<UpRight>(pawnsOn7) & enemies;
                Bitboard b2 = shift<UpLeft>(pawnsOn7) & enemies;

                if (Type == EVASIONS) {
                    b1 &= target;
                    b2 &= target;
                }

                while (b1) {
                    Square to = pop_lsb(b1);
                    Square from = to - UpRight;
                    *moveList++ = make_move(from, to, PROMOTION, QUEEN);
                    *moveList++ = make_move(from, to, PROMOTION, ROOK);
                    *moveList++ = make_move(from, to, PROMOTION, BISHOP);
                    *moveList++ = make_move(from, to, PROMOTION, KNIGHT);
                }

                while (b2) {
                    Square to = pop_lsb(b2);
                    Square from = to - UpLeft;
                    *moveList++ = make_move(from, to, PROMOTION, QUEEN);
                    *moveList++ = make_move(from, to, PROMOTION, ROOK);
                    *moveList++ = make_move(from, to, PROMOTION, BISHOP);
                    *moveList++ = make_move(from, to, PROMOTION, KNIGHT);
                }
            }

            // Promotion pushes
            if (Type != CAPTURES) {
                Bitboard b1 = shift<Up>(pawnsOn7) & emptySquares;
                if (Type == EVASIONS)
                    b1 &= target;

                while (b1) {
                    Square to = pop_lsb(b1);
                    Square from = to - Up;
                    *moveList++ = make_move(from, to, PROMOTION, QUEEN);
                    *moveList++ = make_move(from, to, PROMOTION, ROOK);
                    *moveList++ = make_move(from, to, PROMOTION, BISHOP);
                    *moveList++ = make_move(from, to, PROMOTION, KNIGHT);
                }
            }
        }

        // Standard captures
        if (Type == CAPTURES || Type == EVASIONS || Type == NON_EVASIONS) {
            Bitboard b1 = shift<UpRight>(pawnsNotOn7) & enemies;
            Bitboard b2 = shift<UpLeft>(pawnsNotOn7) & enemies;

            if (Type == EVASIONS) {
                b1 &= target;
                b2 &= target;
            }

            while (b1) {
                Square to = pop_lsb(b1);
                *moveList++ = make_move(to - UpRight, to);
            }

            while (b2) {
                Square to = pop_lsb(b2);
                *moveList++ = make_move(to - UpLeft, to);
            }

            // En passant captures
            if (pos.ep_square() != SQ_NONE) {
                Square epSquare = pos.ep_square();
                
                if (Type == EVASIONS && !(target & square_bb(epSquare - Up)))
                    return moveList;

                b1 = pawnsNotOn7 & pawn_attacks_bb(Them, epSquare);

                while (b1) {
                    Square from = pop_lsb(b1);
                    *moveList++ = make_move(from, epSquare, ENPASSANT);
                }
            }
        }

        return moveList;
    }

    template<PieceType Pt, bool Checks>
    Move* generate_moves(const Position& pos, Move* moveList, Color us, Bitboard target) {
        static_assert(Pt != KING && Pt != PAWN, "Unsupported piece type");

        Bitboard bb = pos.pieces(us, Pt);

        while (bb) {
            Square from = pop_lsb(bb);
            Bitboard b = attacks_bb(Pt, from, pos.pieces()) & target;

            if (Checks && (Pt == QUEEN || !(pos.blockers_for_king(~us) & square_bb(from))))
                b &= pos.check_squares(Pt);

            while (b)
                *moveList++ = make_move(from, pop_lsb(b));
        }

        return moveList;
    }

    template<Color Us, bool Checks>
    Move* generate_castling_moves(const Position& pos, Move* moveList, Color us) {
        constexpr bool KingSide = true;
        constexpr bool QueenSide = false;

        if (pos.castling_impeded(Us | (KingSide ? WHITE_OO : WHITE_OOO)) || 
            !pos.can_castle(Us | (KingSide ? WHITE_OO : WHITE_OOO)))
            return moveList;

        Square kfrom = pos.square<KING>(Us);
        Square rfrom = pos.castling_rook_square(Us | (KingSide ? WHITE_OO : WHITE_OOO));
        Square kto = relative_square(Us, KingSide ? SQ_G1 : SQ_C1);
        Bitboard enemies = pos.pieces(~Us);

        Square K = kfrom, rto = relative_square(Us, KingSide ? SQ_F1 : SQ_D1);

        for (Square s = std::min(K, kto); s <= std::max(K, kto); ++s)
            if (s != K && (pos.attackers_to(s) & enemies))
                return moveList;

        if (KingSide && pos.can_castle(Us == WHITE ? WHITE_OO : BLACK_OO))
            *moveList++ = make_move(kfrom, rfrom, CASTLING);
        
        if (!KingSide && pos.can_castle(Us == WHITE ? WHITE_OOO : BLACK_OOO))
            *moveList++ = make_move(kfrom, rfrom, CASTLING);

        return moveList;
    }

    template<Color Us>
    Move* generate_all(const Position& pos, Move* moveList, Bitboard target) {
        constexpr bool Checks = false;

        moveList = generate_pawn_moves<Us, NON_EVASIONS>(pos, moveList, target);
        moveList = generate_moves<KNIGHT, Checks>(pos, moveList, Us, target);
        moveList = generate_moves<BISHOP, Checks>(pos, moveList, Us, target);
        moveList = generate_moves<ROOK, Checks>(pos, moveList, Us, target);
        moveList = generate_moves<QUEEN, Checks>(pos, moveList, Us, target);

        if (Type != QUIET_CHECKS && Type != EVASIONS) {
            Square ksq = pos.square<KING>(Us);
            Bitboard b = attacks_bb(KING, ksq) & target;
            while (b)
                *moveList++ = make_move(ksq, pop_lsb(b));
        }

        if (Type != CAPTURES && Type != EVASIONS && pos.can_castle(Us))
            moveList = generate_castling_moves<Us, Checks>(pos, moveList, Us);

        return moveList;
    }
}

template<>
MoveList& generate<CAPTURES>(const Position& pos, MoveList& moveList) {
    Color us = pos.side_to_move();
    Bitboard target = pos.pieces(~us);

    Move* cur = const_cast<Move*>(moveList.end());

    cur = us == WHITE ? generate_all<WHITE>(pos, cur, target)
                      : generate_all<BLACK>(pos, cur, target);

    return moveList;
}

template<>
MoveList& generate<QUIETS>(const Position& pos, MoveList& moveList) {
    Color us = pos.side_to_move();
    Bitboard target = ~pos.pieces();

    Move* cur = const_cast<Move*>(moveList.end());

    cur = us == WHITE ? generate_all<WHITE>(pos, cur, target)
                      : generate_all<BLACK>(pos, cur, target);

    return moveList;
}

template<>
MoveList& generate<NON_EVASIONS>(const Position& pos, MoveList& moveList) {
    Color us = pos.side_to_move();
    Bitboard target = ~pos.pieces(us);

    Move* cur = const_cast<Move*>(moveList.end());

    cur = us == WHITE ? generate_all<WHITE>(pos, cur, target)
                      : generate_all<BLACK>(pos, cur, target);

    return moveList;
}

template<>
MoveList& generate<LEGAL>(const Position& pos, MoveList& moveList) {
    MoveList pseudoLegal(pos.checkers() ? EVASIONS : NON_EVASIONS);
    generate<NON_EVASIONS>(pos, pseudoLegal);

    Move* cur = const_cast<Move*>(moveList.end());
    for (const Move& m : pseudoLegal)
        if (pos.legal(m))
            *cur++ = m;

    return moveList;
}

uint64_t perft(Position& pos, Depth depth) {
    if (depth == 0)
        return 1;

    MoveList moveList = generate_legal(pos);
    uint64_t nodes = 0;

    for (const Move& m : moveList) {
        StateInfo st;
        pos.do_move(m, st);
        nodes += perft(pos, depth - 1);
        pos.undo_move(m);
    }

    return nodes;
}

#include "search.h"
#include "evaluate.h"
#include "movegen.h"
#include <algorithm>
#include <iostream>
#include <sstream>

// Global search instance
Search search;

// Transposition table implementation
TranspositionTable::TranspositionTable(size_t mbSize) : generation(0) {
    size = (mbSize * 1024 * 1024) / sizeof(TTEntry);
    // Round down to nearest power of 2
    size_t powerOf2 = 1;
    while (powerOf2 <= size) powerOf2 <<= 1;
    size = powerOf2 >> 1;
    
    table = new TTEntry[size];
    clear();
}

TranspositionTable::~TranspositionTable() {
    delete[] table;
}

void TranspositionTable::clear() {
    std::fill(table, table + size, TTEntry{});
}

void TranspositionTable::new_search() {
    generation++;
}

TTEntry* TranspositionTable::probe(uint64_t key, bool& found) {
    TTEntry* tte = &table[key & (size - 1)];
    found = tte->key == key;
    return tte;
}

void TranspositionTable::store(uint64_t key, Move move, Value value, Value eval, Depth depth, Bound bound) {
    TTEntry* tte = &table[key & (size - 1)];
    
    // Replace if empty, deeper, or same depth but newer generation
    if (!tte->key || tte->depth <= depth || tte->generation != generation) {
        tte->key = key;
        tte->move = move;
        tte->value = value;
        tte->eval = eval;
        tte->depth = depth;
        tte->bound = bound;
        tte->generation = generation;
    }
}

int TranspositionTable::hashfull() const {
    int count = 0;
    for (int i = 0; i < 1000; ++i) {
        if (table[i].key && table[i].generation == generation)
            count++;
    }
    return count;
}

// Search implementation
Search::Search() : tt(16) {
    clear();
}

Search::~Search() = default;

void Search::clear() {
    tt.clear();
    std::fill(&killers[0][0], &killers[MAX_PLY][0], Move(0));
    std::fill(&history[0][0][0], &history[2][0][0], 0);
    std::fill(&pv[0][0], &pv[MAX_PLY][0], Move(0));
    std::fill(pvLength, pvLength + MAX_PLY, 0);
}

SearchResult Search::think(Position& pos, const SearchLimits& limits) {
    info.clear();
    info.limits = limits;
    tt.new_search();
    
    SearchResult result;
    result.bestMove = Move(0);
    result.score = VALUE_ZERO;
    result.depth = 0;
    result.nodes = 0;
    result.time = 0;
    
    // Iterative deepening
    for (Depth depth = 1; depth <= (limits.depth ? limits.depth : 64); ++depth) {
        if (info.should_stop())
            break;
        
        Value score = search_root(pos, -VALUE_INFINITE, VALUE_INFINITE, depth);
        
        result.depth = depth;
        result.score = score;
        result.nodes = info.nodes.load();
        result.time = info.elapsed();
        
        if (pvLength[0] > 0) {
            result.bestMove = pv[0][0];
            
            // Build PV string
            std::ostringstream ss;
            for (int i = 0; i < pvLength[0]; ++i) {
                if (i > 0) ss << " ";
                // Convert move to algebraic notation (simplified)
                Move m = pv[0][i];
                ss << char('a' + file_of(from_sq(m))) << char('1' + rank_of(from_sq(m)))
                   << char('a' + file_of(to_sq(m))) << char('1' + rank_of(to_sq(m)));
                if (type_of(m) == PROMOTION) {
                    const char promotionPieces[] = "nbrq";
                    ss << promotionPieces[promotion_type(m) - KNIGHT];
                }
            }
            result.pv = ss.str();
        }
        
        // Output search info
        std::cout << "info depth " << depth 
                  << " score cp " << score 
                  << " nodes " << result.nodes
                  << " time " << result.time
                  << " pv " << result.pv << std::endl;
        
        // Stop if mate found
        if (is_mate_score(score))
            break;
        
        // Check time limits
        if (limits.movetime && result.time >= limits.movetime * 0.8)
            break;
    }
    
    return result;
}

void Search::stop() {
    info.stop = true;
}

Value Search::search_root(Position& pos, Value alpha, Value beta, Depth depth) {
    pvLength[0] = 0;
    
    MoveList moveList = generate_legal(pos);
    if (moveList.empty())
        return pos.checkers() ? mated_in(0) : VALUE_DRAW;
    
    // Order moves
    Move* begin = const_cast<Move*>(moveList.begin());
    Move* end = const_cast<Move*>(moveList.end());
    order_moves(pos, begin, end, Move(0), 0);
    
    Value bestScore = -VALUE_INFINITE;
    Move bestMove = *begin;
    
    for (const Move& m : moveList) {
        if (info.should_stop())
            break;
        
        StateInfo st;
        pos.do_move(m, st);
        
        Value score = -search(pos, -beta, -alpha, depth - 1, 1);
        
        pos.undo_move(m);
        
        if (score > bestScore) {
            bestScore = score;
            bestMove = m;
            
            // Update PV
            pv[0][0] = m;
            for (int i = 0; i < pvLength[1]; ++i)
                pv[0][i + 1] = pv[1][i];
            pvLength[0] = pvLength[1] + 1;
            
            if (score > alpha) {
                alpha = score;
                if (score >= beta)
                    break;
            }
        }
    }
    
    return bestScore;
}

Value Search::search(Position& pos, Value alpha, Value beta, Depth depth, int ply) {
    if (info.should_stop())
        return VALUE_ZERO;
    
    info.nodes++;
    pvLength[ply] = 0;
    
    // Check for draw
    if (ply > 0 && (pos.is_draw(ply) || is_repetition(pos, ply)))
        return VALUE_DRAW;
    
    // Mate distance pruning
    alpha = std::max(mated_in(ply), alpha);
    beta = std::min(mate_in(ply + 1), beta);
    if (alpha >= beta)
        return alpha;
    
    // Quiescence search at leaf nodes
    if (depth <= 0)
        return qsearch(pos, alpha, beta, ply);
    
    // Transposition table lookup
    bool ttHit;
    TTEntry* tte = tt.probe(pos.key(), ttHit);
    Move ttMove = ttHit ? tte->move : Move(0);
    Value ttValue = ttHit ? tte->value : VALUE_NONE;
    
    if (ttHit && tte->depth >= depth) {
        if (tte->bound == BOUND_EXACT ||
            (tte->bound == BOUND_LOWER && ttValue >= beta) ||
            (tte->bound == BOUND_UPPER && ttValue <= alpha))
            return ttValue;
    }
    
    // Generate moves
    MoveList moveList = generate_legal(pos);
    if (moveList.empty())
        return pos.checkers() ? mated_in(ply) : VALUE_DRAW;
    
    // Order moves
    Move* begin = const_cast<Move*>(moveList.begin());
    Move* end = const_cast<Move*>(moveList.end());
    order_moves(pos, begin, end, ttMove, ply);
    
    Value bestScore = -VALUE_INFINITE;
    Move bestMove = Move(0);
    Bound bound = BOUND_UPPER;
    
    for (const Move& m : moveList) {
        if (info.should_stop())
            break;
        
        StateInfo st;
        pos.do_move(m, st);
        
        Value score = -search(pos, -beta, -alpha, depth - 1, ply + 1);
        
        pos.undo_move(m);
        
        if (score > bestScore) {
            bestScore = score;
            bestMove = m;
            
            if (score > alpha) {
                alpha = score;
                bound = BOUND_EXACT;
                
                // Update PV
                pv[ply][0] = m;
                for (int i = 0; i < pvLength[ply + 1]; ++i)
                    pv[ply][i + 1] = pv[ply + 1][i];
                pvLength[ply] = pvLength[ply + 1] + 1;
                
                if (score >= beta) {
                    bound = BOUND_LOWER;
                    
                    // Update killer moves and history
                    if (pos.piece_on(to_sq(m)) == NO_PIECE) {
                        killers[ply][1] = killers[ply][0];
                        killers[ply][0] = m;
                        history[pos.side_to_move()][from_sq(m)][to_sq(m)] += depth * depth;
                    }
                    
                    break;
                }
            }
        }
    }
    
    // Store in transposition table
    tt.store(pos.key(), bestMove, bestScore, VALUE_NONE, depth, bound);
    
    return bestScore;
}

Value Search::qsearch(Position& pos, Value alpha, Value beta, int ply) {
    if (info.should_stop())
        return VALUE_ZERO;
    
    info.nodes++;
    pvLength[ply] = 0;
    
    // Stand pat
    Value standPat = evaluate(pos);
    
    if (standPat >= beta)
        return beta;
    
    if (standPat > alpha)
        alpha = standPat;
    
    // Generate captures
    MoveList moveList = generate_captures(pos);
    
    // Order captures by MVV-LVA
    Move* begin = const_cast<Move*>(moveList.begin());
    Move* end = const_cast<Move*>(moveList.end());
    std::sort(begin, end, [&](Move a, Move b) {
        return move_value(pos, a, Move(0)) > move_value(pos, b, Move(0));
    });
    
    for (const Move& m : moveList) {
        if (info.should_stop())
            break;
        
        // Delta pruning
        if (standPat + PIECE_VALUES[type_of(pos.piece_on(to_sq(m)))] + 200 < alpha)
            continue;
        
        StateInfo st;
        pos.do_move(m, st);
        
        Value score = -qsearch(pos, -beta, -alpha, ply + 1);
        
        pos.undo_move(m);
        
        if (score >= beta)
            return beta;
        
        if (score > alpha)
            alpha = score;
    }
    
    return alpha;
}

void Search::order_moves(const Position& pos, Move* begin, Move* end, Move ttMove, int ply) {
    std::sort(begin, end, [&](Move a, Move b) {
        return move_value(pos, a, ttMove) > move_value(pos, b, ttMove);
    });
}

Value Search::move_value(const Position& pos, Move m, Move ttMove) {
    if (m == ttMove)
        return 10000;
    
    Square to = to_sq(m);
    Piece captured = pos.piece_on(to);
    
    if (captured != NO_PIECE) {
        // MVV-LVA for captures
        return MVV_LVA[type_of(pos.piece_on(from_sq(m)))][type_of(captured)];
    }
    
    // Killer moves
    if (m == killers[0][0] || m == killers[0][1])
        return 900;
    
    // History heuristic
    return history[pos.side_to_move()][from_sq(m)][to_sq(m)];
}

bool Search::is_repetition(const Position& pos, int ply) {
    // Simplified repetition detection
    return false; // TODO: Implement proper repetition detection
}

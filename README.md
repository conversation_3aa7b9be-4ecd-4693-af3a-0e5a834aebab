# Chess Engine

A high-performance chess engine implementation in C++ featuring bitboard representation, minimax search with alpha-beta pruning, and UCI protocol support.

## Features

- **Bitboard-based board representation** for efficient position manipulation
- **Complete move generation** for all piece types including special moves (castling, en passant, promotion)
- **Legal move validation** with check detection and pin handling
- **Position evaluation** with material counting, piece-square tables, and mobility
- **Minimax search** with alpha-beta pruning and iterative deepening
- **Transposition table** for position caching and search optimization
- **UCI protocol** support for communication with chess GUIs
- **Move ordering** with MVV-LVA, killer moves, and history heuristic

## Architecture

### Core Components

1. **Types and Constants** (`types.h`)
   - Basic type definitions (Bitboard, Square, Piece, Move, etc.)
   - Chess constants and utility functions
   - Move encoding/decoding functions

2. **Bitboard Operations** (`bitboard.h/cpp`)
   - Efficient bitboard manipulation functions
   - Magic bitboard implementation for sliding pieces
   - Attack generation and lookup tables

3. **Position Representation** (`position.h/cpp`)
   - Complete chess position state management
   - FEN parsing and generation
   - Move making/unmaking with state restoration
   - <PERSON><PERSON><PERSON> hashing for position identification

4. **Move Generation** (`movegen.h/cpp`)
   - Legal move generation for all piece types
   - Special move handling (castling, en passant, promotion)
   - Move list management and filtering

5. **Position Evaluation** (`evaluate.h/cpp`)
   - Material counting and piece-square tables
   - Mobility evaluation for pieces
   - Basic positional factors

6. **Search Engine** (`search.h/cpp`)
   - Minimax algorithm with alpha-beta pruning
   - Iterative deepening framework
   - Transposition table integration
   - Quiescence search for tactical positions

7. **UCI Protocol** (`uci.h/cpp`)
   - Universal Chess Interface implementation
   - Command parsing and response handling
   - Engine option management

## Building

### Using Make (Linux/macOS/Windows with MinGW)

```bash
# Build release version
make

# Build debug version
make debug

# Clean build files
make clean

# Run tests
make test

# Run benchmark
make bench

# Run perft test
make perft
```

### Using CMake (Cross-platform)

```bash
# Create build directory
mkdir build && cd build

# Configure
cmake ..

# Build
cmake --build .

# Run tests
ctest
```

### Manual Compilation

```bash
g++ -std=c++17 -O3 -march=native -pthread \
    src/*.cpp -o ChessEngine
```

## Usage

### UCI Mode (Default)

Run the engine and communicate via UCI protocol:

```bash
./ChessEngine
```

Common UCI commands:
- `uci` - Initialize UCI mode
- `isready` - Check if engine is ready
- `position startpos` - Set starting position
- `position fen <fen_string>` - Set position from FEN
- `go depth 10` - Search to depth 10
- `go movetime 5000` - Search for 5 seconds
- `quit` - Exit engine

### Testing

```bash
# Run basic functionality tests
./ChessEngine test

# Run performance benchmark
./ChessEngine bench

# Run perft test to verify move generation
./ChessEngine perft 6
```

## Chess GUI Integration

The engine supports the UCI protocol and can be used with popular chess GUIs:

- **Arena Chess GUI**
- **ChessBase/Fritz**
- **Cute Chess**
- **PyChess**
- **Scid vs. PC**

To add the engine to a GUI:
1. Compile the engine
2. In your chess GUI, add a new engine
3. Point to the ChessEngine executable
4. The engine will automatically use UCI protocol

## Algorithm Details

### Board Representation

- **Bitboards**: 64-bit integers representing piece positions
- **Magic Bitboards**: Efficient sliding piece attack generation
- **Zobrist Hashing**: Position identification for transposition table

### Search Algorithm

- **Minimax with Alpha-Beta Pruning**: Core search algorithm
- **Iterative Deepening**: Gradually increase search depth
- **Quiescence Search**: Extend search in tactical positions
- **Transposition Table**: Cache previously evaluated positions

### Move Ordering

1. **Hash Move**: Best move from transposition table
2. **Captures**: Ordered by MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
3. **Killer Moves**: Non-capture moves that caused beta cutoffs
4. **History Heuristic**: Moves that historically performed well

### Evaluation Function

- **Material**: Piece values (P=100, N=320, B=330, R=500, Q=900)
- **Piece-Square Tables**: Positional bonuses for piece placement
- **Mobility**: Bonus for piece movement options
- **King Safety**: Penalty for exposed king
- **Pawn Structure**: Basic pawn evaluation

## Performance

The engine is optimized for performance with:
- Efficient bitboard operations
- Magic bitboard move generation
- Transposition table caching
- Move ordering optimizations
- Compiler optimizations (-O3, -march=native)

Typical performance on modern hardware:
- **Move Generation**: ~10M moves/second
- **Search Speed**: ~1M nodes/second
- **Memory Usage**: ~20MB (configurable)

## Configuration

Engine options (configurable via UCI):
- `Hash`: Transposition table size in MB (default: 16)
- `Threads`: Number of search threads (default: 1)
- `Ponder`: Enable pondering mode (default: false)

## Development

### Code Structure

```
src/
├── types.h          # Basic types and constants
├── bitboard.h/cpp   # Bitboard operations
├── position.h/cpp   # Position representation
├── movegen.h/cpp    # Move generation
├── evaluate.h/cpp   # Position evaluation
├── search.h/cpp     # Search algorithm
├── uci.h/cpp        # UCI protocol
└── main.cpp         # Main entry point

tests/
└── test_basic.cpp   # Basic functionality tests
```

### Adding Features

To extend the engine:
1. **Evaluation**: Add new evaluation terms in `evaluate.cpp`
2. **Search**: Implement new pruning techniques in `search.cpp`
3. **Move Generation**: Optimize move generation in `movegen.cpp`
4. **UCI Options**: Add new options in `uci.cpp`

## License

This chess engine is provided as an educational implementation. Feel free to use, modify, and distribute according to your needs.

## Contributing

Contributions are welcome! Areas for improvement:
- Advanced evaluation features
- Search optimizations
- Endgame tablebase support
- Multi-threading
- Opening book integration

## Acknowledgments

This implementation draws inspiration from:
- Stockfish chess engine architecture
- Chess Programming Wiki
- Bitboard techniques from various sources
- UCI protocol specification

cmake_minimum_required(VERSION 3.10)
project(ChessEngine)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Compiler flags
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /O2 /W3")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2 /DNDEBUG")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -O3 -Wall -Wextra -march=native")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG -flto")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -O0 -g")
endif()

# Source files
set(SOURCES
    src/main.cpp
    src/bitboard.cpp
    src/position.cpp
    src/movegen.cpp
    src/evaluate.cpp
    src/search.cpp
    src/uci.cpp
)

# Header files
set(HEADERS
    src/types.h
    src/bitboard.h
    src/position.h
    src/movegen.h
    src/evaluate.h
    src/search.h
    src/uci.h
)

# Create executable
add_executable(ChessEngine ${SOURCES} ${HEADERS})

# Link libraries
if(NOT MSVC)
    target_link_libraries(ChessEngine pthread)
endif()

# Set output directory
set_target_properties(ChessEngine PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Install target
install(TARGETS ChessEngine DESTINATION bin)

# Testing
enable_testing()

# Add test executable
add_executable(ChessEngineTest
    tests/test_main.cpp
    tests/test_bitboard.cpp
    tests/test_position.cpp
    tests/test_movegen.cpp
    tests/test_evaluate.cpp
    ${SOURCES}
)

# Remove main.cpp from test build
list(REMOVE_ITEM SOURCES src/main.cpp)
target_sources(ChessEngineTest PRIVATE ${SOURCES})

if(NOT MSVC)
    target_link_libraries(ChessEngineTest pthread)
endif()

add_test(NAME ChessEngineTests COMMAND ChessEngineTest)

# Benchmark target
add_custom_target(bench
    COMMAND ChessEngine bench
    DEPENDS ChessEngine
    COMMENT "Running benchmark"
)

# Perft target
add_custom_target(perft
    COMMAND ChessEngine perft 6
    DEPENDS ChessEngine
    COMMENT "Running perft test"
)

# Documentation
find_package(Doxygen)
if(DOXYGEN_FOUND)
    set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/docs/Doxyfile.in)
    set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
    
    configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
    
    add_custom_target(doc_doxygen ALL
        COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
        COMMENT "Generating API documentation with Doxygen"
        VERBATIM
    )
endif()

# Package configuration
set(CPACK_PACKAGE_NAME "ChessEngine")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "High-performance chess engine")
set(CPACK_PACKAGE_VENDOR "Chess Engine Developer")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

include(CPack)

#include "uci.h"
#include "bitboard.h"
#include "evaluate.h"
#include "search.h"
#include <iostream>

int main(int argc, char* argv[]) {
    // Initialize engine components
    Bitboards::init();
    init_eval();
    init_uci();
    
    std::cout << "ChessEngine 1.0 by Chess Engine Developer" << std::endl;
    
    // Check for command line arguments
    if (argc > 1) {
        std::string arg = argv[1];
        
        if (arg == "bench") {
            // Run benchmark
            std::cout << "Running benchmark..." << std::endl;
            // TODO: Implement benchmark
            return 0;
        } else if (arg == "test") {
            // Run tests
            std::cout << "Running tests..." << std::endl;
            // TODO: Implement tests
            return 0;
        } else if (arg == "perft") {
            // Run perft test
            if (argc > 2) {
                int depth = std::stoi(argv[2]);
                std::cout << "Running perft to depth " << depth << std::endl;
                // TODO: Implement perft test
            }
            return 0;
        }
    }
    
    // Start UCI loop
    uci.loop();
    
    return 0;
}

#include "position.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <cctype>

namespace
{
    // Zobrist random numbers for hashing
    uint64_t zobrist[PIECE_NB][64];
    uint64_t zobristEnpassant[8];
    uint64_t zobristCastling[16];
    uint64_t zobristSideToMove;

    // Initialize Zobrist keys
    void init_zobrist()
    {
        static bool initialized = false;
        if (initialized)
            return;

        // Simple PRNG for generating pseudo-random numbers
        uint64_t seed = 1070372;
        auto rng = [&seed]()
        {
            seed = (seed * 1103515245 + 12345) & 0x7fffffff;
            return seed;
        };

        for (int pc = NO_PIECE; pc < PIECE_NB; ++pc)
            for (int s = 0; s < 64; ++s)
                zobrist[pc][s] = ((uint64_t)rng() << 32) | rng();

        for (int f = 0; f < 8; ++f)
            zobristEnpassant[f] = ((uint64_t)rng() << 32) | rng();

        for (int cr = 0; cr < 16; ++cr)
            zobristCastling[cr] = ((uint64_t)rng() << 32) | rng();

        zobristSideToMove = ((uint64_t)rng() << 32) | rng();
        initialized = true;
    }

    // Convert piece character to piece type
    PieceType char_to_piece_type(char c)
    {
        switch (std::tolower(c))
        {
        case 'p':
            return PAWN;
        case 'n':
            return KNIGHT;
        case 'b':
            return BISHOP;
        case 'r':
            return ROOK;
        case 'q':
            return QUEEN;
        case 'k':
            return KING;
        default:
            return NO_PIECE_TYPE;
        }
    }

    // Convert piece to character
    char piece_to_char(Piece pc)
    {
        const std::string pieces = " PNBRQK  pnbrqk";
        return pieces[pc];
    }
}

void Position::clear()
{
    std::fill(std::begin(board), std::end(board), NO_PIECE);
    std::fill(std::begin(byTypeBB), std::end(byTypeBB), 0);
    std::fill(std::begin(byColorBB), std::end(byColorBB), 0);
    std::fill(std::begin(pieceCount), std::end(pieceCount), 0);

    for (int pc = 0; pc < PIECE_NB; ++pc)
        for (int i = 0; i < 16; ++i)
            pieceList[pc][i] = SQ_NONE;

    std::fill(std::begin(index), std::end(index), 0);

    sideToMove = WHITE;
    gamePly = 0;
    chess960 = false;
    st = nullptr;
    key_ = materialKey_ = pawnKey_ = 0;
}

void Position::set_startpos(StateInfo *si)
{
    set("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1", si);
}

void Position::set(const std::string &fenStr, StateInfo *si)
{
    init_zobrist();
    clear();

    std::istringstream ss(fenStr);
    std::string token;

    // 1. Piece placement
    ss >> token;
    Square sq = SQ_A8;
    for (char c : token)
    {
        if (std::isdigit(c))
            sq += (c - '0'); // Empty squares
        else if (c == '/')
            sq -= 16; // Next rank
        else
        {
            PieceType pt = char_to_piece_type(c);
            if (pt != NO_PIECE_TYPE)
            {
                Color color = std::islower(c) ? BLACK : WHITE;
                put_piece(make_piece(color, pt), sq);
                ++sq;
            }
        }
    }

    // 2. Active color
    ss >> token;
    sideToMove = (token == "w") ? WHITE : BLACK;

    // 3. Castling availability
    ss >> token;
    st = si;
    st->castlingRights = 0;
    for (char c : token)
    {
        switch (c)
        {
        case 'K':
            st->castlingRights |= WHITE_OO;
            break;
        case 'Q':
            st->castlingRights |= WHITE_OOO;
            break;
        case 'k':
            st->castlingRights |= BLACK_OO;
            break;
        case 'q':
            st->castlingRights |= BLACK_OOO;
            break;
        }
    }

    // 4. En passant target square
    ss >> token;
    if (token == "-")
        st->epSquare = SQ_NONE;
    else
        st->epSquare = make_square(token[0] - 'a', token[1] - '1');

    // 5-6. Halfmove and fullmove clocks
    ss >> std::skipws >> st->rule50 >> gamePly;
    gamePly = std::max(2 * (gamePly - 1), 0) + (sideToMove == BLACK);

    // Initialize remaining state
    st->capturedPiece = NO_PIECE;
    st->previous = nullptr;
    st->pliesFromNull = 0;

    // Compute non-pawn material
    for (Color c = WHITE; c <= BLACK; ++c)
    {
        st->nonPawnMaterial[c] = 0;
        for (PieceType pt = KNIGHT; pt <= QUEEN; ++pt)
            st->nonPawnMaterial[c] += count<KNIGHT>(c) * PIECE_VALUES[KNIGHT] + count<BISHOP>(c) * PIECE_VALUES[BISHOP] + count<ROOK>(c) * PIECE_VALUES[ROOK] + count<QUEEN>(c) * PIECE_VALUES[QUEEN];
    }

    set_check_info(st);
    compute_key();
    compute_material_key();
    compute_pawn_key();
}

void Position::put_piece(Piece pc, Square s)
{
    board[s] = pc;
    byTypeBB[ALL_PIECES] |= square_bb(s);
    byTypeBB[type_of(pc)] |= square_bb(s);
    byColorBB[color_of(pc)] |= square_bb(s);
    index[s] = pieceCount[pc]++;
    pieceList[pc][index[s]] = s;
}

void Position::remove_piece(Square s)
{
    Piece pc = board[s];
    byTypeBB[ALL_PIECES] ^= square_bb(s);
    byTypeBB[type_of(pc)] ^= square_bb(s);
    byColorBB[color_of(pc)] ^= square_bb(s);

    Square lastSquare = pieceList[pc][--pieceCount[pc]];
    index[lastSquare] = index[s];
    pieceList[pc][index[lastSquare]] = lastSquare;
    pieceList[pc][pieceCount[pc]] = SQ_NONE;
    board[s] = NO_PIECE;
}

void Position::move_piece(Square from, Square to)
{
    Piece pc = board[from];
    Bitboard fromTo = square_bb(from) ^ square_bb(to);
    byTypeBB[ALL_PIECES] ^= fromTo;
    byTypeBB[type_of(pc)] ^= fromTo;
    byColorBB[color_of(pc)] ^= fromTo;
    board[from] = NO_PIECE;
    board[to] = pc;
    index[to] = index[from];
    pieceList[pc][index[to]] = to;
}

Bitboard Position::attackers_to(Square s) const
{
    return attackers_to(s, pieces());
}

Bitboard Position::attackers_to(Square s, Bitboard occupied) const
{
    return (pawn_attacks_bb(BLACK, s) & pieces(WHITE, PAWN)) | (pawn_attacks_bb(WHITE, s) & pieces(BLACK, PAWN)) | (attacks_bb(KNIGHT, s) & pieces(KNIGHT)) | (attacks_bb(ROOK, s, occupied) & pieces(ROOK, QUEEN)) | (attacks_bb(BISHOP, s, occupied) & pieces(BISHOP, QUEEN)) | (attacks_bb(KING, s) & pieces(KING));
}

bool Position::legal(Move m) const
{
    Color us = sideToMove;
    Square from = from_sq(m);
    Square to = to_sq(m);

    if (type_of(m) == ENPASSANT)
    {
        Square ksq = square<KING>(us);
        Square capsq = to - pawn_push(us);
        Bitboard occupied = (pieces() ^ square_bb(from) ^ square_bb(capsq)) | square_bb(to);

        return !(attacks_bb(ROOK, ksq, occupied) & pieces(~us, QUEEN, ROOK)) && !(attacks_bb(BISHOP, ksq, occupied) & pieces(~us, QUEEN, BISHOP));
    }

    if (type_of(piece_on(from)) == KING)
    {
        if (type_of(m) == CASTLING)
            return true; // Castling legality is checked in move generation

        return !(attackers_to(to, pieces() ^ square_bb(from)) & pieces(~us));
    }

    return !(blockers_for_king(us) & square_bb(from)) || aligned(from, to, square<KING>(us));
}

void Position::set_check_info(StateInfo *si) const
{
    si->checkersBB = attackers_to(square<KING>(sideToMove)) & pieces(~sideToMove);
}

Bitboard Position::blockers_for_king(Color c) const
{
    Bitboard blockers = 0;
    Bitboard snipers = ((pieces(ROOK, QUEEN) & attacks_bb(ROOK, square<KING>(c))) | (pieces(BISHOP, QUEEN) & attacks_bb(BISHOP, square<KING>(c)))) & pieces(~c);

    Bitboard occupancy = pieces() ^ snipers;

    while (snipers)
    {
        Square sniperSq = pop_lsb(snipers);
        Bitboard b = between_bb(square<KING>(c), sniperSq) & occupancy;

        if (b && !more_than_one(b))
            blockers |= b & pieces(c);
    }
    return blockers;
}

void Position::compute_key()
{
    key_ = 0;

    for (Square s = SQ_A1; s <= SQ_H8; ++s)
        if (piece_on(s) != NO_PIECE)
            key_ ^= zobrist[piece_on(s)][s];

    if (ep_square() != SQ_NONE)
        key_ ^= zobristEnpassant[file_of(ep_square())];

    key_ ^= zobristCastling[castling_rights()];

    if (sideToMove == BLACK)
        key_ ^= zobristSideToMove;
}

void Position::compute_material_key()
{
    materialKey_ = 0;
    for (Color c = WHITE; c <= BLACK; ++c)
        for (PieceType pt = PAWN; pt <= KING; ++pt)
            for (int cnt = 0; cnt < count(c, pt); ++cnt)
                materialKey_ ^= zobrist[make_piece(c, pt)][cnt];
}

void Position::compute_pawn_key()
{
    pawnKey_ = 0;
    Bitboard pawns = pieces(PAWN);
    while (pawns)
    {
        Square s = pop_lsb(pawns);
        pawnKey_ ^= zobrist[piece_on(s)][s];
    }
}

std::string Position::fen() const
{
    std::ostringstream ss;

    // 1. Piece placement
    for (int r = RANK_8; r >= RANK_1; --r)
    {
        for (int f = FILE_A; f <= FILE_H; ++f)
        {
            int emptyCnt = 0;
            for (; f <= FILE_H && empty(make_square(f, r)); ++f)
                ++emptyCnt;

            if (emptyCnt)
                ss << emptyCnt;

            if (f <= FILE_H)
                ss << piece_to_char(piece_on(make_square(f, r)));
        }

        if (r > RANK_1)
            ss << '/';
    }

    // 2. Active color
    ss << (sideToMove == WHITE ? " w " : " b ");

    // 3. Castling availability
    if (castling_rights())
    {
        if (can_castle(WHITE_OO))
            ss << 'K';
        if (can_castle(WHITE_OOO))
            ss << 'Q';
        if (can_castle(BLACK_OO))
            ss << 'k';
        if (can_castle(BLACK_OOO))
            ss << 'q';
    }
    else
    {
        ss << '-';
    }

    // 4. En passant target square
    ss << (ep_square() == SQ_NONE ? " -" : " " + std::string(1, 'a' + file_of(ep_square())) + std::string(1, '1' + rank_of(ep_square())));

    // 5-6. Halfmove and fullmove clocks
    ss << " " << st->rule50 << " " << 1 + (gamePly - (sideToMove == BLACK)) / 2;

    return ss.str();
}

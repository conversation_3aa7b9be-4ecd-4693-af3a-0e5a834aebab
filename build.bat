@echo off
echo Building Chess Engine...

REM Create directories
if not exist "obj" mkdir obj
if not exist "bin" mkdir bin

REM Check for Visual Studio compiler
where cl >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using MSVC compiler...
    cl /std:c++17 /O2 /EHsc /Fe:bin\ChessEngine.exe src\*.cpp
    goto :end
)

REM Check for MinGW compiler
where g++ >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using MinGW compiler...
    g++ -std=c++17 -O3 -Wall -Wextra -o bin\ChessEngine.exe src\*.cpp
    goto :end
)

REM Check for Clang compiler
where clang++ >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo Using Clang compiler...
    clang++ -std=c++17 -O3 -Wall -Wextra -o bin\ChessEngine.exe src\*.cpp
    goto :end
)

echo Error: No C++ compiler found!
echo Please install one of the following:
echo - Visual Studio (MSVC)
echo - MinGW-w64
echo - Clang
exit /b 1

:end
if %ERRORLEVEL% EQU 0 (
    echo Build successful! Executable created at bin\ChessEngine.exe
) else (
    echo Build failed!
    exit /b 1
)

#ifndef BITBOARD_H
#define BITBOARD_H

#include "types.h"
#include <string>

// Bitboard manipulation functions and lookup tables
namespace Bitboards
{
    void init();
    std::string pretty(Bitboard b);
}

// Bitboard operations
constexpr Bitboard shift(Bitboard b, int delta)
{
    return delta > 0 ? b << delta : b >> -delta;
}

template <int Delta>
constexpr Bitboard shift(Bitboard b)
{
    return Delta > 0 ? b << Delta : b >> -Delta;
}

// Population count (number of set bits)
inline int popcount(Bitboard b)
{
#if defined(__GNUC__)
    return __builtin_popcountll(b);
#elif defined(_MSC_VER)
    return (int)__popcnt64(b);
#else
    // Software fallback
    int count = 0;
    while (b)
    {
        count++;
        b &= b - 1; // Clear the lowest set bit
    }
    return count;
#endif
}

// Find and clear the least significant bit
inline Square lsb(Bitboard b)
{
#if defined(__GNUC__)
    return Square(__builtin_ctzll(b));
#elif defined(_MSC_VER)
    unsigned long idx;
    _BitScanForward64(&idx, b);
    return Square(idx);
#else
    // Software fallback
    const int index64[64] = {
        0, 47, 1, 56, 48, 27, 2, 60,
        57, 49, 41, 37, 28, 16, 3, 61,
        54, 58, 35, 52, 50, 42, 21, 44,
        38, 32, 29, 23, 17, 11, 4, 62,
        46, 55, 26, 59, 40, 36, 15, 53,
        34, 51, 20, 43, 31, 22, 10, 45,
        25, 39, 14, 33, 19, 30, 9, 24,
        13, 18, 8, 12, 7, 6, 5, 63};
    const Bitboard debruijn64 = 0x03f566f7a88c7922ULL;
    return Square(index64[((b ^ (b - 1)) * debruijn64) >> 58]);
#endif
}

// Find and clear the most significant bit
inline Square msb(Bitboard b)
{
#if defined(__GNUC__)
    return Square(63 ^ __builtin_clzll(b));
#elif defined(_MSC_VER)
    unsigned long idx;
    _BitScanReverse64(&idx, b);
    return Square(idx);
#else
    // Software fallback
    Square result = SQ_NONE;
    while (b)
    {
        result = lsb(b);
        b &= b - 1;
    }
    return result;
#endif
}

// Pop the least significant bit and return its square
inline Square pop_lsb(Bitboard &b)
{
    Square s = lsb(b);
    b &= b - 1;
    return s;
}

// External lookup tables (defined in bitboard.cpp)
extern Bitboard SquareBB[64];
extern Bitboard FileBB[8];
extern Bitboard RankBB[8];
extern Bitboard AdjacentFilesBB[8];
extern Bitboard ForwardRanksBB[2][8];
extern Bitboard BetweenBB[64][64];
extern Bitboard LineBB[64][64];
extern Bitboard DistanceRingBB[64][8];
extern Bitboard ForwardFileBB[2][64];
extern Bitboard PassedPawnMask[2][64];
extern Bitboard PawnAttacksBB[2][64];
extern Bitboard PseudoAttacks[PIECE_TYPE_NB][64];
extern Bitboard KingAttacks[64];
extern Bitboard KnightAttacks[64];

// Magic bitboard structures for sliding pieces
struct Magic
{
    Bitboard *attacks;
    Bitboard mask;
    Bitboard magic;
    int shift;

    unsigned index(Bitboard occupied) const
    {
        return unsigned(((occupied & mask) * magic) >> shift);
    }
};

extern Magic RookMagics[64];
extern Magic BishopMagics[64];

// Attack generation functions
inline Bitboard square_bb_lookup(Square s)
{
    return SquareBB[s];
}

inline Bitboard file_bb(Square s)
{
    return FileBB[file_of(s)];
}

inline Bitboard rank_bb(Square s)
{
    return RankBB[rank_of(s)];
}

inline Bitboard adjacent_files_bb(Square s)
{
    return AdjacentFilesBB[file_of(s)];
}

inline Bitboard forward_ranks_bb(Color c, Square s)
{
    return ForwardRanksBB[c][rank_of(s)];
}

inline Bitboard forward_file_bb(Color c, Square s)
{
    return ForwardFileBB[c][s];
}

inline Bitboard pawn_attack_span(Color c, Square s)
{
    return forward_ranks_bb(c, s) & adjacent_files_bb(s);
}

inline Bitboard passed_pawn_mask(Color c, Square s)
{
    return PassedPawnMask[c][s];
}

inline Bitboard attacks_bb(PieceType pt, Square s, Bitboard occupied = 0)
{
    switch (pt)
    {
    case BISHOP:
        return BishopMagics[s].attacks[BishopMagics[s].index(occupied)];
    case ROOK:
        return RookMagics[s].attacks[RookMagics[s].index(occupied)];
    case QUEEN:
        return attacks_bb(BISHOP, s, occupied) | attacks_bb(ROOK, s, occupied);
    default:
        return PseudoAttacks[pt][s];
    }
}

inline Bitboard between_bb(Square s1, Square s2)
{
    return BetweenBB[s1][s2];
}

inline Bitboard line_bb(Square s1, Square s2)
{
    return LineBB[s1][s2];
}

inline bool aligned(Square s1, Square s2, Square s3)
{
    return LineBB[s1][s2] & square_bb(s3);
}

inline Bitboard distance_ring_bb(Square s, int d)
{
    return DistanceRingBB[s][d];
}

// Pawn attack functions
inline Bitboard pawn_attacks_bb(Color c, Square s)
{
    return PawnAttacksBB[c][s];
}

template <Color C>
constexpr Bitboard pawn_attacks_bb(Bitboard b)
{
    return C == WHITE ? shift<NORTH_WEST>(b & ~FileABB) | shift<NORTH_EAST>(b & ~FileHBB)
                      : shift<SOUTH_WEST>(b & ~FileABB) | shift<SOUTH_EAST>(b & ~FileHBB);
}

// Safe destination squares for a piece on 's' (not attacked by enemy pawns)
inline Bitboard safe_destination(Square s, Bitboard occupied)
{
    return ~(pawn_attacks_bb(WHITE, s) | pawn_attacks_bb(BLACK, s)) & ~occupied;
}

#endif // BITBOARD_H

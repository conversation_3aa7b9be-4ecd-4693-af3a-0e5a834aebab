#ifndef MOVEGEN_H
#define MOVEGEN_H

#include "types.h"
#include "position.h"

// Move generation types
enum GenType {
    CAPTURES,
    QUIETS,
    QUIET_CHECKS,
    EVASIONS,
    NON_EVASIONS,
    LEGAL
};

// Move list class for storing generated moves
class MoveList {
public:
    explicit MoveList(GenType gt) : last(moveList), genType(gt) {}
    
    const Move* begin() const { return moveList; }
    const Move* end() const { return last; }
    size_t size() const { return last - moveList; }
    bool empty() const { return last == moveList; }
    
    Move& operator[](size_t index) { return moveList[index]; }
    const Move& operator[](size_t index) const { return moveList[index]; }
    
    void push_back(Move m) { *last++ = m; }
    
private:
    Move moveList[256];
    Move* last;
    GenType genType;
};

// Move generation functions
template<GenType>
MoveList& generate(const Position& pos, MoveList& moveList);

// Explicit instantiations
template<> MoveList& generate<CAPTURES>(const Position& pos, MoveList& moveList);
template<> MoveList& generate<QUIETS>(const Position& pos, MoveList& moveList);
template<> MoveList& generate<NON_EVASIONS>(const Position& pos, MoveList& moveList);
template<> MoveList& generate<EVASIONS>(const Position& pos, MoveList& moveList);
template<> MoveList& generate<LEGAL>(const Position& pos, MoveList& moveList);

// Helper functions for move generation
namespace {
    template<Color Us, GenType Type>
    Move* generate_pawn_moves(const Position& pos, Move* moveList, Bitboard target);
    
    template<PieceType Pt, bool Checks>
    Move* generate_moves(const Position& pos, Move* moveList, Color us, Bitboard target);
    
    template<Color Us, bool Checks>
    Move* generate_castling_moves(const Position& pos, Move* moveList, Color us);
    
    template<Color Us>
    Move* generate_all(const Position& pos, Move* moveList, Bitboard target);
}

// Utility functions
bool is_legal(const Position& pos, Move m);
Bitboard check_squares(const Position& pos, PieceType pt);

// Move generation for specific piece types
template<Color Us>
Bitboard pawn_attacks_from(Square s) {
    return pawn_attacks_bb(Us, s);
}

template<PieceType Pt>
Bitboard attacks_from(Square s) {
    return attacks_bb(Pt, s);
}

template<PieceType Pt>
Bitboard attacks_from(Square s, Bitboard occupied) {
    return attacks_bb(Pt, s, occupied);
}

// Inline implementations
inline bool is_legal(const Position& pos, Move m) {
    return pos.legal(m);
}

// Move generation entry points
inline MoveList generate_captures(const Position& pos) {
    MoveList moveList(CAPTURES);
    return generate<CAPTURES>(pos, moveList);
}

inline MoveList generate_quiets(const Position& pos) {
    MoveList moveList(QUIETS);
    return generate<QUIETS>(pos, moveList);
}

inline MoveList generate_non_evasions(const Position& pos) {
    MoveList moveList(NON_EVASIONS);
    return generate<NON_EVASIONS>(pos, moveList);
}

inline MoveList generate_evasions(const Position& pos) {
    MoveList moveList(EVASIONS);
    return generate<EVASIONS>(pos, moveList);
}

inline MoveList generate_legal(const Position& pos) {
    MoveList moveList(LEGAL);
    return generate<LEGAL>(pos, moveList);
}

// Check if a move gives check
bool gives_check(const Position& pos, Move m);

// Perft function for testing move generation
uint64_t perft(Position& pos, Depth depth);

#endif // MOVEGEN_H

#include "../src/types.h"
#include "../src/bitboard.h"
#include "../src/position.h"
#include "../src/movegen.h"
#include "../src/evaluate.h"
#include <iostream>
#include <cassert>

void test_bitboards() {
    std::cout << "Testing bitboards..." << std::endl;
    
    // Test basic bitboard operations
    Bitboard bb = square_bb(SQ_E4);
    assert(bb == (1ULL << SQ_E4));
    
    // Test popcount
    assert(popcount(0x0F) == 4);
    assert(popcount(0xFF) == 8);
    
    // Test lsb/msb
    assert(lsb(0x10) == 4);
    assert(msb(0x10) == 4);
    
    std::cout << "Bitboard tests passed!" << std::endl;
}

void test_position() {
    std::cout << "Testing position..." << std::endl;
    
    StateInfo st;
    Position pos;
    pos.set_startpos(&st);
    
    // Test starting position
    assert(pos.piece_on(SQ_E1) == W_KING);
    assert(pos.piece_on(SQ_E8) == B_KING);
    assert(pos.piece_on(SQ_A1) == W_ROOK);
    assert(pos.piece_on(SQ_H8) == B_ROOK);
    
    // Test piece counts
    assert(pos.count<PAWN>(WHITE) == 8);
    assert(pos.count<PAWN>(BLACK) == 8);
    assert(pos.count<KING>(WHITE) == 1);
    assert(pos.count<KING>(BLACK) == 1);
    
    std::cout << "Position tests passed!" << std::endl;
}

void test_move_generation() {
    std::cout << "Testing move generation..." << std::endl;
    
    StateInfo st;
    Position pos;
    pos.set_startpos(&st);
    
    // Generate moves from starting position
    MoveList moveList = generate_legal(pos);
    
    // Starting position should have 20 legal moves
    assert(moveList.size() == 20);
    
    std::cout << "Move generation tests passed!" << std::endl;
}

void test_evaluation() {
    std::cout << "Testing evaluation..." << std::endl;
    
    StateInfo st;
    Position pos;
    pos.set_startpos(&st);
    
    // Evaluate starting position (should be close to 0)
    Value eval = evaluate(pos);
    assert(std::abs(eval) < 100); // Should be relatively balanced
    
    std::cout << "Evaluation tests passed!" << std::endl;
}

void test_perft() {
    std::cout << "Testing perft..." << std::endl;
    
    StateInfo st;
    Position pos;
    pos.set_startpos(&st);
    
    // Perft(1) from starting position should be 20
    uint64_t nodes = perft(pos, 1);
    assert(nodes == 20);
    
    // Perft(2) from starting position should be 400
    nodes = perft(pos, 2);
    assert(nodes == 400);
    
    std::cout << "Perft tests passed!" << std::endl;
}

int main() {
    std::cout << "Running Chess Engine Tests..." << std::endl;
    
    // Initialize engine
    Bitboards::init();
    init_eval();
    
    try {
        test_bitboards();
        test_position();
        test_move_generation();
        test_evaluation();
        test_perft();
        
        std::cout << "\nAll tests passed successfully!" << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception!" << std::endl;
        return 1;
    }
}

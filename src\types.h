#ifndef TYPES_H
#define TYPES_H

#include <cstdint>
#include <string>

// Basic type definitions for the chess engine
using Bitboard = uint64_t;
using Square = int;
using Piece = int;
using Color = int;
using PieceType = int;
using Move = uint32_t;
using Value = int;
using Depth = int;

// Constants for colors
constexpr Color WHITE = 0;
constexpr Color BLACK = 1;
constexpr Color NO_COLOR = 2;

// Constants for piece types
constexpr PieceType NO_PIECE_TYPE = 0;
constexpr PieceType PAWN = 1;
constexpr PieceType KNIGHT = 2;
constexpr PieceType BISHOP = 3;
constexpr PieceType ROOK = 4;
constexpr PieceType QUEEN = 5;
constexpr PieceType KING = 6;
constexpr PieceType ALL_PIECES = 0;
constexpr PieceType PIECE_TYPE_NB = 7;

// Constants for pieces (color + piece type)
constexpr Piece NO_PIECE = 0;
constexpr Piece W_PAWN = 1;
constexpr Piece W_KNIGHT = 2;
constexpr Piece W_BISHOP = 3;
constexpr Piece W_ROOK = 4;
constexpr Piece W_QUEEN = 5;
constexpr Piece W_KING = 6;
constexpr Piece B_PAWN = 9;
constexpr Piece B_KNIGHT = 10;
constexpr Piece B_BISHOP = 11;
constexpr Piece B_ROOK = 12;
constexpr Piece B_QUEEN = 13;
constexpr Piece B_KING = 14;
constexpr Piece PIECE_NB = 16;

// Square constants (a1 = 0, h8 = 63)
constexpr Square SQ_A1 = 0, SQ_B1 = 1, SQ_C1 = 2, SQ_D1 = 3, SQ_E1 = 4, SQ_F1 = 5, SQ_G1 = 6, SQ_H1 = 7;
constexpr Square SQ_A8 = 56, SQ_B8 = 57, SQ_C8 = 58, SQ_D8 = 59, SQ_E8 = 60, SQ_F8 = 61, SQ_G8 = 62, SQ_H8 = 63;
constexpr Square SQ_NONE = 64;

// Castling rights
constexpr int WHITE_OO = 1;
constexpr int WHITE_OOO = 2;
constexpr int BLACK_OO = 4;
constexpr int BLACK_OOO = 8;
constexpr int CASTLING_RIGHT_NB = 16;

// Move types
constexpr int NORMAL = 0;
constexpr int PROMOTION = 1;
constexpr int ENPASSANT = 2;
constexpr int CASTLING = 3;

// Evaluation constants
constexpr Value VALUE_ZERO = 0;
constexpr Value VALUE_DRAW = 0;
constexpr Value VALUE_MATE = 32000;
constexpr Value VALUE_INFINITE = 32001;
constexpr Value VALUE_NONE = 32002;

// Piece values in centipawns
constexpr Value PIECE_VALUES[PIECE_TYPE_NB] = {
    0, 100, 320, 330, 500, 900, 20000};

// Direction constants for piece movement
constexpr int NORTH = 8;
constexpr int SOUTH = -8;
constexpr int EAST = 1;
constexpr int WEST = -1;
constexpr int NORTH_EAST = 9;
constexpr int NORTH_WEST = 7;
constexpr int SOUTH_EAST = -7;
constexpr int SOUTH_WEST = -9;

// Utility functions
constexpr Color operator~(Color c) { return Color(c ^ 1); }
constexpr Piece make_piece(Color c, PieceType pt) { return Piece((c << 3) + pt); }
constexpr PieceType type_of(Piece pc) { return PieceType(pc & 7); }
constexpr Color color_of(Piece pc) { return Color(pc >> 3); }
constexpr Square make_square(int file, int rank) { return Square((rank << 3) + file); }
constexpr int file_of(Square s) { return s & 7; }
constexpr int rank_of(Square s) { return s >> 3; }
constexpr Square relative_square(Color c, Square s) { return Square(s ^ (c * 56)); }
constexpr int relative_rank(Color c, Square s) { return rank_of(relative_square(c, s)); }
constexpr int pawn_push(Color c) { return c == WHITE ? NORTH : SOUTH; }

// Bitboard utility functions
constexpr Bitboard square_bb(Square s) { return 1ULL << s; }
constexpr bool more_than_one(Bitboard b) { return b & (b - 1); }

// Move encoding/decoding
constexpr Move make_move(Square from, Square to)
{
    return Move((from << 6) + to);
}

constexpr Move make_move(Square from, Square to, int type, PieceType promotion = KNIGHT)
{
    return Move(type << 14 | promotion << 12 | from << 6 | to);
}

constexpr Square from_sq(Move m) { return Square((m >> 6) & 0x3F); }
constexpr Square to_sq(Move m) { return Square(m & 0x3F); }
constexpr int type_of(Move m) { return m >> 14; }
constexpr PieceType promotion_type(Move m) { return PieceType((m >> 12) & 3); }

// File and rank constants
constexpr int FILE_A = 0, FILE_B = 1, FILE_C = 2, FILE_D = 3;
constexpr int FILE_E = 4, FILE_F = 5, FILE_G = 6, FILE_H = 7;
constexpr int RANK_1 = 0, RANK_2 = 1, RANK_3 = 2, RANK_4 = 3;
constexpr int RANK_5 = 4, RANK_6 = 5, RANK_7 = 6, RANK_8 = 7;

// Bitboard constants
constexpr Bitboard FileABB = 0x0101010101010101ULL;
constexpr Bitboard FileBBB = FileABB << 1;
constexpr Bitboard FileCBB = FileABB << 2;
constexpr Bitboard FileDBB = FileABB << 3;
constexpr Bitboard FileEBB = FileABB << 4;
constexpr Bitboard FileFBB = FileABB << 5;
constexpr Bitboard FileGBB = FileABB << 6;
constexpr Bitboard FileHBB = FileABB << 7;

constexpr Bitboard Rank1BB = 0xFF;
constexpr Bitboard Rank2BB = Rank1BB << (8 * 1);
constexpr Bitboard Rank3BB = Rank1BB << (8 * 2);
constexpr Bitboard Rank4BB = Rank1BB << (8 * 3);
constexpr Bitboard Rank5BB = Rank1BB << (8 * 4);
constexpr Bitboard Rank6BB = Rank1BB << (8 * 5);
constexpr Bitboard Rank7BB = Rank1BB << (8 * 6);
constexpr Bitboard Rank8BB = Rank1BB << (8 * 7);

#endif // TYPES_H

#include "uci.h"
#include "bitboard.h"
#include "evaluate.h"
#include <iostream>
#include <sstream>
#include <thread>
#include <algorithm>

// Global instances
UCI uci;
std::map<std::string, UCIOption> Options;

// UCIOption implementation
UCIOption::UCIOption(bool defaultValue) 
    : type(OPTION_CHECK), defaultValue(defaultValue ? "true" : "false"), currentValue(this->defaultValue) {}

UCIOption::UCIOption(int defaultValue, int minValue, int maxValue)
    : type(OPTION_SPIN), defaultValue(std::to_string(defaultValue)), currentValue(this->defaultValue),
      minValue(minValue), maxValue(maxValue) {}

UCIOption::UCIOption(const std::string& defaultValue, const std::vector<std::string>& values)
    : type(values.empty() ? OPTION_STRING : OPTION_COMBO), defaultValue(defaultValue), 
      currentValue(defaultValue), comboValues(values) {}

UCIOption& UCIOption::operator=(const std::string& value) {
    currentValue = value;
    return *this;
}

UCIOption::operator bool() const {
    return currentValue == "true";
}

UCIOption::operator int() const {
    return std::stoi(currentValue);
}

UCIOption::operator std::string() const {
    return currentValue;
}

std::string UCIOption::to_uci() const {
    std::ostringstream ss;
    ss << "option name " << " type ";
    
    switch (type) {
        case OPTION_CHECK:
            ss << "check default " << defaultValue;
            break;
        case OPTION_SPIN:
            ss << "spin default " << defaultValue << " min " << minValue << " max " << maxValue;
            break;
        case OPTION_COMBO:
            ss << "combo default " << defaultValue;
            for (const auto& value : comboValues)
                ss << " var " << value;
            break;
        case OPTION_STRING:
            ss << "string default " << defaultValue;
            break;
        case OPTION_BUTTON:
            ss << "button";
            break;
    }
    
    return ss.str();
}

void init_uci() {
    // Initialize UCI options
    Options["Hash"] = UCIOption(16, 1, 1024);
    Options["Threads"] = UCIOption(1, 1, 128);
    Options["Ponder"] = UCIOption(false);
    Options["UCI_Chess960"] = UCIOption(false);
    Options["UCI_AnalyseMode"] = UCIOption(false);
}

UCI::UCI() : statesPtr(0), debug(false), quit(false) {
    pos.set_startpos(&states[statesPtr++]);
}

void UCI::loop() {
    std::string line, token;
    
    while (std::getline(std::cin, line) && !quit) {
        std::istringstream iss(line);
        iss >> token;
        
        if (token == "uci")
            cmd_uci();
        else if (token == "debug")
            cmd_debug(line.substr(6));
        else if (token == "isready")
            cmd_isready();
        else if (token == "setoption")
            cmd_setoption(line.substr(10));
        else if (token == "register")
            cmd_register(line.substr(9));
        else if (token == "ucinewgame")
            cmd_ucinewgame();
        else if (token == "position")
            cmd_position(line.substr(9));
        else if (token == "go")
            cmd_go(line.substr(3));
        else if (token == "stop")
            cmd_stop();
        else if (token == "ponderhit")
            cmd_ponderhit();
        else if (token == "quit")
            cmd_quit();
        else if (debug)
            std::cout << "info string Unknown command: " << token << std::endl;
    }
}

void UCI::cmd_uci() {
    std::cout << "id name " << ENGINE_NAME << " " << ENGINE_VERSION << std::endl;
    std::cout << "id author " << ENGINE_AUTHOR << std::endl;
    
    for (const auto& option : Options)
        std::cout << option.second.to_uci() << std::endl;
    
    std::cout << "uciok" << std::endl;
}

void UCI::cmd_debug(const std::string& args) {
    std::istringstream iss(args);
    std::string token;
    iss >> token;
    debug = (token == "on");
}

void UCI::cmd_isready() {
    std::cout << "readyok" << std::endl;
}

void UCI::cmd_setoption(const std::string& args) {
    std::istringstream iss(args);
    std::string token, name, value;
    
    iss >> token; // "name"
    iss >> name;
    
    // Read multi-word option names
    while (iss >> token && token != "value")
        name += " " + token;
    
    // Read option value
    if (token == "value") {
        std::getline(iss, value);
        if (!value.empty() && value[0] == ' ')
            value = value.substr(1);
    }
    
    if (Options.find(name) != Options.end()) {
        Options[name] = value;
        
        // Handle special options
        if (name == "Hash") {
            // Resize transposition table
        } else if (name == "Threads") {
            // Set number of search threads
        }
    }
}

void UCI::cmd_register(const std::string& args) {
    // Registration not required for this engine
}

void UCI::cmd_ucinewgame() {
    search.clear();
    statesPtr = 0;
    pos.set_startpos(&states[statesPtr++]);
}

void UCI::cmd_position(const std::string& args) {
    std::vector<std::string> tokens = split(args);
    
    if (tokens.empty())
        return;
    
    std::string fen;
    std::vector<std::string> moves;
    
    if (tokens[0] == "startpos") {
        fen = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1";
        
        // Find "moves" keyword
        auto it = std::find(tokens.begin(), tokens.end(), "moves");
        if (it != tokens.end()) {
            moves.assign(it + 1, tokens.end());
        }
    } else if (tokens[0] == "fen") {
        // Build FEN string
        std::ostringstream fenStream;
        size_t i = 1;
        while (i < tokens.size() && tokens[i] != "moves") {
            if (i > 1) fenStream << " ";
            fenStream << tokens[i];
            ++i;
        }
        fen = fenStream.str();
        
        // Get moves if present
        if (i < tokens.size() && tokens[i] == "moves") {
            moves.assign(tokens.begin() + i + 1, tokens.end());
        }
    }
    
    set_position(fen, moves);
}

void UCI::cmd_go(const std::string& args) {
    std::vector<std::string> tokens = split(args);
    SearchLimits limits = parse_go_command(tokens);
    
    // Start search in separate thread
    std::thread searchThread([this, limits]() {
        SearchResult result = search.think(pos, limits);
        std::cout << "bestmove " << move_to_uci(result.bestMove) << std::endl;
    });
    
    searchThread.detach();
}

void UCI::cmd_stop() {
    search.stop();
}

void UCI::cmd_ponderhit() {
    // Convert pondering to normal search
}

void UCI::cmd_quit() {
    search.stop();
    quit = true;
}

std::vector<std::string> UCI::split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::istringstream iss(str);
    std::string token;
    
    while (std::getline(iss, token, delimiter)) {
        if (!token.empty())
            tokens.push_back(token);
    }
    
    return tokens;
}

void UCI::set_position(const std::string& fen, const std::vector<std::string>& moves) {
    statesPtr = 0;
    pos.set(fen, &states[statesPtr++]);
    
    for (const std::string& moveStr : moves) {
        Move m = move_from_uci(pos, moveStr);
        if (m != Move(0)) {
            pos.do_move(m, states[statesPtr++]);
        }
    }
}

SearchLimits UCI::parse_go_command(const std::vector<std::string>& tokens) {
    SearchLimits limits;
    
    for (size_t i = 0; i < tokens.size(); ++i) {
        const std::string& token = tokens[i];
        
        if (token == "wtime" && i + 1 < tokens.size())
            limits.time[WHITE] = std::stoi(tokens[++i]);
        else if (token == "btime" && i + 1 < tokens.size())
            limits.time[BLACK] = std::stoi(tokens[++i]);
        else if (token == "winc" && i + 1 < tokens.size())
            limits.inc[WHITE] = std::stoi(tokens[++i]);
        else if (token == "binc" && i + 1 < tokens.size())
            limits.inc[BLACK] = std::stoi(tokens[++i]);
        else if (token == "movestogo" && i + 1 < tokens.size())
            limits.movestogo = std::stoi(tokens[++i]);
        else if (token == "depth" && i + 1 < tokens.size())
            limits.depth = std::stoi(tokens[++i]);
        else if (token == "nodes" && i + 1 < tokens.size())
            limits.nodes = std::stoull(tokens[++i]);
        else if (token == "movetime" && i + 1 < tokens.size())
            limits.movetime = std::stoi(tokens[++i]);
        else if (token == "infinite")
            limits.infinite = true;
        else if (token == "ponder")
            limits.ponder = true;
    }
    
    return limits;
}

Move move_from_uci(const Position& pos, const std::string& str) {
    if (str.length() < 4)
        return Move(0);
    
    Square from = make_square(str[0] - 'a', str[1] - '1');
    Square to = make_square(str[2] - 'a', str[3] - '1');
    
    // Check for promotion
    PieceType promotion = KNIGHT;
    if (str.length() == 5) {
        switch (str[4]) {
            case 'n': promotion = KNIGHT; break;
            case 'b': promotion = BISHOP; break;
            case 'r': promotion = ROOK; break;
            case 'q': promotion = QUEEN; break;
        }
    }
    
    // Generate legal moves and find matching move
    MoveList moveList = generate_legal(pos);
    for (const Move& m : moveList) {
        if (from_sq(m) == from && to_sq(m) == to) {
            if (type_of(m) == PROMOTION && promotion_type(m) == promotion)
                return m;
            else if (type_of(m) != PROMOTION)
                return m;
        }
    }
    
    return Move(0);
}

std::string move_to_uci(Move m) {
    if (m == Move(0))
        return "0000";
    
    std::string result;
    result += char('a' + file_of(from_sq(m)));
    result += char('1' + rank_of(from_sq(m)));
    result += char('a' + file_of(to_sq(m)));
    result += char('1' + rank_of(to_sq(m)));
    
    if (type_of(m) == PROMOTION) {
        const char promotionPieces[] = "nbrq";
        result += promotionPieces[promotion_type(m) - KNIGHT];
    }
    
    return result;
}

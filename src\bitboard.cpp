#include "bitboard.h"
#include <iostream>
#include <iomanip>

// Global bitboard lookup tables
Bitboard SquareBB[64];
Bitboard FileBB[8];
Bitboard RankBB[8];
Bitboard AdjacentFilesBB[8];
Bitboard ForwardRanksBB[2][8];
Bitboard BetweenBB[64][64];
Bitboard LineBB[64][64];
Bitboard DistanceRingBB[64][8];
Bitboard ForwardFileBB[2][64];
Bitboard PassedPawnMask[2][64];
Bitboard PawnAttacksBB[2][64];
Bitboard PseudoAttacks[PIECE_TYPE_NB][64];
Bitboard KingAttacks[64];
Bitboard KnightAttacks[64];

Magic RookMagics[64];
Magic BishopMagics[64];

namespace {
    // Magic numbers for rook attacks (pre-computed)
    const Bitboard RookMagicNumbers[64] = {
        0x0080001020400080ULL, 0x0040001000200040ULL, 0x0080081000200080ULL, 0x0080040800100080ULL,
        0x0080020400080080ULL, 0x0080010200040080ULL, 0x0080008001000200ULL, 0x0080002040800100ULL,
        0x0000800020400080ULL, 0x0000400020005000ULL, 0x0000801000200080ULL, 0x0000800800100080ULL,
        0x0000800400080080ULL, 0x0000800200040080ULL, 0x0000800100020080ULL, 0x0000800040800100ULL,
        0x0000208000400080ULL, 0x0000404000201000ULL, 0x0000808010002000ULL, 0x0000808008001000ULL,
        0x0000808004000800ULL, 0x0000808002000400ULL, 0x0000010100020004ULL, 0x0000020000408104ULL,
        0x0000208080004000ULL, 0x0000200040005000ULL, 0x0000100080200080ULL, 0x0000080080100080ULL,
        0x0000040080080080ULL, 0x0000020080040080ULL, 0x0000010080800200ULL, 0x0000800080004100ULL,
        0x0000204000800080ULL, 0x0000200040401000ULL, 0x0000100080802000ULL, 0x0000080080801000ULL,
        0x0000040080800800ULL, 0x0000020080800400ULL, 0x0000020001010004ULL, 0x0000800040800100ULL,
        0x0000204000808000ULL, 0x0000200040008080ULL, 0x0000100020008080ULL, 0x0000080010008080ULL,
        0x0000040008008080ULL, 0x0000020004008080ULL, 0x0000010002008080ULL, 0x0000004081020004ULL,
        0x0000204000800080ULL, 0x0000200040008080ULL, 0x0000100020008080ULL, 0x0000080010008080ULL,
        0x0000040008008080ULL, 0x0000020004008080ULL, 0x0000800100020080ULL, 0x0000800041000080ULL,
        0x00FFFCDDFCED714AULL, 0x007FFCDDFCED714AULL, 0x003FFFCDFFD88096ULL, 0x0000040810002101ULL,
        0x0001000204080011ULL, 0x0001000204000801ULL, 0x0001000082000401ULL, 0x0001FFFAABFAD1A2ULL
    };

    // Magic numbers for bishop attacks (pre-computed)
    const Bitboard BishopMagicNumbers[64] = {
        0x0002020202020200ULL, 0x0002020202020000ULL, 0x0004010202000000ULL, 0x0004040080000000ULL,
        0x0001104000000000ULL, 0x0000821040000000ULL, 0x0000410410400000ULL, 0x0000104104104000ULL,
        0x0000040404040400ULL, 0x0000020202020200ULL, 0x0000040102020000ULL, 0x0000040400800000ULL,
        0x0000011040000000ULL, 0x0000008210400000ULL, 0x0000004104104000ULL, 0x0000002082082000ULL,
        0x0004000808080800ULL, 0x0002000404040400ULL, 0x0001000202020200ULL, 0x0000800802004000ULL,
        0x0000800400A00000ULL, 0x0000200100884000ULL, 0x0000400082082000ULL, 0x0000200041041000ULL,
        0x0002080010101000ULL, 0x0001040008080800ULL, 0x0000208004010400ULL, 0x0000404004010200ULL,
        0x0000840000802000ULL, 0x0000404002011000ULL, 0x0000808001041000ULL, 0x0000404000820800ULL,
        0x0001041000202000ULL, 0x0000820800101000ULL, 0x0000104400080800ULL, 0x0000020080080080ULL,
        0x0000404040040100ULL, 0x0000808100020100ULL, 0x0001010100020800ULL, 0x0000808080010400ULL,
        0x0000820820004000ULL, 0x0000410410002000ULL, 0x0000082088001000ULL, 0x0000002011000800ULL,
        0x0000080100400400ULL, 0x0001010101000200ULL, 0x0002020202000400ULL, 0x0001010101000200ULL,
        0x0000410410400000ULL, 0x0000208208200000ULL, 0x0000002084100000ULL, 0x0000000020880000ULL,
        0x0000001002020000ULL, 0x0000040408020000ULL, 0x0004040404040000ULL, 0x0002020202020000ULL,
        0x0000104104104000ULL, 0x0000002082082000ULL, 0x0000000020841000ULL, 0x0000000000208800ULL,
        0x0000000010020200ULL, 0x0000000404080200ULL, 0x0000040404040400ULL, 0x0002020202020200ULL
    };

    // Rook attack masks and shifts
    const int RookShifts[64] = {
        52, 53, 53, 53, 53, 53, 53, 52,
        53, 54, 54, 54, 54, 54, 54, 53,
        53, 54, 54, 54, 54, 54, 54, 53,
        53, 54, 54, 54, 54, 54, 54, 53,
        53, 54, 54, 54, 54, 54, 54, 53,
        53, 54, 54, 54, 54, 54, 54, 53,
        53, 54, 54, 54, 54, 54, 54, 53,
        52, 53, 53, 53, 53, 53, 53, 52
    };

    // Bishop attack masks and shifts
    const int BishopShifts[64] = {
        58, 59, 59, 59, 59, 59, 59, 58,
        59, 59, 59, 59, 59, 59, 59, 59,
        59, 59, 57, 57, 57, 57, 59, 59,
        59, 59, 57, 55, 55, 57, 59, 59,
        59, 59, 57, 55, 55, 57, 59, 59,
        59, 59, 57, 57, 57, 57, 59, 59,
        59, 59, 59, 59, 59, 59, 59, 59,
        58, 59, 59, 59, 59, 59, 59, 58
    };

    Bitboard sliding_attack(Square sq, Bitboard occupied, const int deltas[]) {
        Bitboard attack = 0;
        
        for (int i = 0; i < 4; ++i) {
            int delta = deltas[i];
            for (Square s = sq + delta; 
                 s >= 0 && s < 64 && distance(s, s - delta) == 1; 
                 s += delta) {
                attack |= square_bb(s);
                if (occupied & square_bb(s))
                    break;
            }
        }
        return attack;
    }

    int distance(Square s1, Square s2) {
        return std::max(std::abs(file_of(s1) - file_of(s2)), 
                       std::abs(rank_of(s1) - rank_of(s2)));
    }

    void init_magics(Magic magics[], const Bitboard magic_numbers[], 
                     const int shifts[], const int deltas[]) {
        Bitboard occupancy[4096], reference[4096];
        Bitboard* attacks = new Bitboard[88772]; // Total size needed
        int size = 0;

        for (Square s = SQ_A1; s <= SQ_H8; ++s) {
            Magic& m = magics[s];
            m.magic = magic_numbers[s];
            m.shift = shifts[s];
            m.attacks = attacks + size;

            // Generate mask (remove edge squares for sliding pieces)
            m.mask = 0;
            for (int delta : {deltas[0], deltas[1], deltas[2], deltas[3]}) {
                for (Square sq = s + delta; 
                     sq >= 0 && sq < 64 && distance(sq, sq - delta) == 1; 
                     sq += delta) {
                    if (deltas == (int[]){NORTH, SOUTH, EAST, WEST}) { // Rook
                        if (rank_of(sq) != RANK_1 && rank_of(sq) != RANK_8 &&
                            file_of(sq) != FILE_A && file_of(sq) != FILE_H)
                            m.mask |= square_bb(sq);
                    } else { // Bishop
                        if (rank_of(sq) != RANK_1 && rank_of(sq) != RANK_8 &&
                            file_of(sq) != FILE_A && file_of(sq) != FILE_H)
                            m.mask |= square_bb(sq);
                    }
                }
            }

            int n = popcount(m.mask);
            size += 1 << n;

            // Generate all possible occupancy patterns
            for (int i = 0; i < (1 << n); ++i) {
                occupancy[i] = 0;
                Bitboard mask = m.mask;
                for (int j = 0; j < n; ++j) {
                    Square sq = pop_lsb(mask);
                    if (i & (1 << j))
                        occupancy[i] |= square_bb(sq);
                }
                reference[i] = sliding_attack(s, occupancy[i], deltas);
            }

            // Fill attack table
            for (int i = 0; i < (1 << n); ++i) {
                unsigned index = m.index(occupancy[i]);
                m.attacks[index] = reference[i];
            }
        }
    }
}

void Bitboards::init() {
    // Initialize square bitboards
    for (Square s = SQ_A1; s <= SQ_H8; ++s)
        SquareBB[s] = 1ULL << s;

    // Initialize file and rank bitboards
    for (int f = FILE_A; f <= FILE_H; ++f)
        FileBB[f] = FileABB << f;
    
    for (int r = RANK_1; r <= RANK_8; ++r)
        RankBB[r] = Rank1BB << (8 * r);

    // Initialize adjacent files
    for (int f = FILE_A; f <= FILE_H; ++f) {
        AdjacentFilesBB[f] = 0;
        if (f > FILE_A) AdjacentFilesBB[f] |= FileBB[f - 1];
        if (f < FILE_H) AdjacentFilesBB[f] |= FileBB[f + 1];
    }

    // Initialize forward ranks
    for (Color c = WHITE; c <= BLACK; ++c)
        for (int r = RANK_1; r <= RANK_8; ++r) {
            ForwardRanksBB[c][r] = 0;
            for (int rr = r + (c == WHITE ? 1 : -1); 
                 rr >= RANK_1 && rr <= RANK_8; 
                 rr += (c == WHITE ? 1 : -1))
                ForwardRanksBB[c][r] |= RankBB[rr];
        }

    // Initialize king attacks
    for (Square s = SQ_A1; s <= SQ_H8; ++s) {
        KingAttacks[s] = 0;
        for (int delta : {-9, -8, -7, -1, 1, 7, 8, 9}) {
            Square to = s + delta;
            if (to >= 0 && to < 64 && distance(s, to) <= 2)
                KingAttacks[s] |= square_bb(to);
        }
        PseudoAttacks[KING][s] = KingAttacks[s];
    }

    // Initialize knight attacks
    for (Square s = SQ_A1; s <= SQ_H8; ++s) {
        KnightAttacks[s] = 0;
        for (int delta : {-17, -15, -10, -6, 6, 10, 15, 17}) {
            Square to = s + delta;
            if (to >= 0 && to < 64 && distance(s, to) <= 3)
                KnightAttacks[s] |= square_bb(to);
        }
        PseudoAttacks[KNIGHT][s] = KnightAttacks[s];
    }

    // Initialize pawn attacks
    for (Square s = SQ_A1; s <= SQ_H8; ++s) {
        PawnAttacksBB[WHITE][s] = 0;
        PawnAttacksBB[BLACK][s] = 0;
        
        if (rank_of(s) < RANK_8) {
            if (file_of(s) > FILE_A) PawnAttacksBB[WHITE][s] |= square_bb(s + 7);
            if (file_of(s) < FILE_H) PawnAttacksBB[WHITE][s] |= square_bb(s + 9);
        }
        
        if (rank_of(s) > RANK_1) {
            if (file_of(s) > FILE_A) PawnAttacksBB[BLACK][s] |= square_bb(s - 9);
            if (file_of(s) < FILE_H) PawnAttacksBB[BLACK][s] |= square_bb(s - 7);
        }
    }

    // Initialize magic bitboards for sliding pieces
    int rook_deltas[] = {NORTH, SOUTH, EAST, WEST};
    int bishop_deltas[] = {NORTH_EAST, NORTH_WEST, SOUTH_EAST, SOUTH_WEST};
    
    init_magics(RookMagics, RookMagicNumbers, RookShifts, rook_deltas);
    init_magics(BishopMagics, BishopMagicNumbers, BishopShifts, bishop_deltas);

    // Initialize between and line bitboards
    for (Square s1 = SQ_A1; s1 <= SQ_H8; ++s1)
        for (Square s2 = SQ_A1; s2 <= SQ_H8; ++s2) {
            BetweenBB[s1][s2] = 0;
            LineBB[s1][s2] = 0;
            
            if (s1 == s2) continue;
            
            Bitboard bb = (attacks_bb(ROOK, s1) & attacks_bb(ROOK, s2)) |
                         (attacks_bb(BISHOP, s1) & attacks_bb(BISHOP, s2));
            
            if (bb & square_bb(s2)) {
                LineBB[s1][s2] = (attacks_bb(ROOK, s1) & attacks_bb(ROOK, s2)) |
                                (attacks_bb(BISHOP, s1) & attacks_bb(BISHOP, s2)) |
                                square_bb(s1) | square_bb(s2);
                
                BetweenBB[s1][s2] = attacks_bb(ROOK, s1, square_bb(s2)) & 
                                   attacks_bb(ROOK, s2, square_bb(s1));
                BetweenBB[s1][s2] |= attacks_bb(BISHOP, s1, square_bb(s2)) & 
                                    attacks_bb(BISHOP, s2, square_bb(s1));
            }
        }
}

std::string Bitboards::pretty(Bitboard b) {
    std::string s = "+---+---+---+---+---+---+---+---+\n";
    
    for (int r = RANK_8; r >= RANK_1; --r) {
        for (int f = FILE_A; f <= FILE_H; ++f)
            s += "| " + std::string((b & square_bb(make_square(f, r)) ? "X" : " ")) + " ";
        s += "| " + std::to_string(1 + r) + "\n+---+---+---+---+---+---+---+---+\n";
    }
    s += "  a   b   c   d   e   f   g   h\n";
    
    return s;
}

#ifndef POSITION_H
#define POSITION_H

#include "types.h"
#include "bitboard.h"
#include <string>
#include <vector>

// Forward declarations
class Position;

// Structure to hold game state information that cannot be recovered from the position
struct StateInfo {
    // Copied when making a move
    Square epSquare;
    int castlingRights;
    int rule50;
    int pliesFromNull;
    Value nonPawnMaterial[2];
    
    // Not copied when making a move (will be recomputed)
    Bitboard checkersBB;
    Piece capturedPiece;
    StateInfo* previous;
};

// The Position class represents a chess position
class Position {
public:
    // Constructors and setup
    Position() = default;
    Position(const Position&) = delete;
    Position& operator=(const Position&) = delete;
    
    // Position setup
    void set(const std::string& fenStr, StateInfo* si);
    void set_startpos(StateInfo* si);
    void clear();
    
    // Position information
    Piece piece_on(Square s) const;
    Square square(PieceType pt, Color c) const;
    bool empty(Square s) const;
    template<PieceType Pt> int count(Color c) const;
    template<PieceType Pt> Square square(Color c) const;
    
    // Bitboard access
    Bitboard pieces() const;
    Bitboard pieces(PieceType pt) const;
    Bitboard pieces(PieceType pt1, PieceType pt2) const;
    Bitboard pieces(Color c) const;
    Bitboard pieces(Color c, PieceType pt) const;
    Bitboard pieces(Color c, PieceType pt1, PieceType pt2) const;
    
    // Game state
    Color side_to_move() const;
    int game_ply() const;
    bool is_chess960() const;
    Square ep_square() const;
    int castling_rights() const;
    int castling_rights(Color c) const;
    bool can_castle(int cr) const;
    bool castling_impeded(int cr) const;
    Square castling_rook_square(int cr) const;
    
    // Position validation and legality
    bool legal(Move m) const;
    bool pseudo_legal(Move m) const;
    bool gives_check(Move m) const;
    bool is_draw(int ply) const;
    bool has_game_cycle(int ply) const;
    bool has_repeated() const;
    
    // Attacks and checks
    Bitboard attackers_to(Square s) const;
    Bitboard attackers_to(Square s, Bitboard occupied) const;
    Bitboard checkers() const;
    Bitboard blockers_for_king(Color c) const;
    Bitboard check_squares(PieceType pt) const;
    bool is_discovery_check_on_king(Color c, Move m) const;
    
    // Making and unmaking moves
    void do_move(Move m, StateInfo& newSt);
    void do_move(Move m, StateInfo& newSt, bool givesCheck);
    void undo_move(Move m);
    void do_null_move(StateInfo& newSt);
    void undo_null_move();
    
    // Static exchange evaluation
    Value see(Move m) const;
    Value see_ge(Move m, Value threshold) const;
    
    // Position evaluation helpers
    Value psq_score() const;
    Value non_pawn_material() const;
    Value non_pawn_material(Color c) const;
    
    // Zobrist hashing
    uint64_t key() const;
    uint64_t key_after(Move m) const;
    uint64_t material_key() const;
    uint64_t pawn_key() const;
    
    // Position representation
    std::string fen() const;
    std::string pretty() const;
    
    // Debugging
    bool pos_is_ok() const;
    void flip();

private:
    // Board representation
    Piece board[64];
    Bitboard byTypeBB[PIECE_TYPE_NB];
    Bitboard byColorBB[2];
    int pieceCount[PIECE_NB];
    Square pieceList[PIECE_NB][16];
    int index[64];
    
    // Game state
    Color sideToMove;
    int gamePly;
    bool chess960;
    StateInfo* st;
    
    // Zobrist keys
    uint64_t key_;
    uint64_t materialKey_;
    uint64_t pawnKey_;
    
    // Helper functions
    void put_piece(Piece pc, Square s);
    void remove_piece(Square s);
    void move_piece(Square from, Square to);
    template<bool Do> void do_castling(Color us, Square from, Square& to, Square& rfrom, Square& rto);
    void set_castling_right(Color c, Square rfrom);
    void set_state(StateInfo* si);
    void set_check_info(StateInfo* si) const;
    
    // Position validation
    bool legal(Move m, Bitboard pinned) const;
    
    // Static exchange evaluation helpers
    Value see_ge(Move m, Bitboard& occupied, Value threshold) const;
    
    // Zobrist hashing helpers
    void compute_key();
    void compute_material_key();
    void compute_pawn_key();
};

// Inline implementations
inline Piece Position::piece_on(Square s) const {
    return board[s];
}

inline bool Position::empty(Square s) const {
    return piece_on(s) == NO_PIECE;
}

inline Color Position::side_to_move() const {
    return sideToMove;
}

inline Bitboard Position::pieces() const {
    return byColorBB[WHITE] | byColorBB[BLACK];
}

inline Bitboard Position::pieces(PieceType pt) const {
    return byTypeBB[pt];
}

inline Bitboard Position::pieces(PieceType pt1, PieceType pt2) const {
    return byTypeBB[pt1] | byTypeBB[pt2];
}

inline Bitboard Position::pieces(Color c) const {
    return byColorBB[c];
}

inline Bitboard Position::pieces(Color c, PieceType pt) const {
    return byColorBB[c] & byTypeBB[pt];
}

inline Bitboard Position::pieces(Color c, PieceType pt1, PieceType pt2) const {
    return byColorBB[c] & (byTypeBB[pt1] | byTypeBB[pt2]);
}

template<PieceType Pt>
inline int Position::count(Color c) const {
    return pieceCount[make_piece(c, Pt)];
}

template<PieceType Pt>
inline Square Position::square(Color c) const {
    return pieceList[make_piece(c, Pt)][0];
}

inline Square Position::ep_square() const {
    return st->epSquare;
}

inline int Position::castling_rights() const {
    return st->castlingRights;
}

inline int Position::castling_rights(Color c) const {
    return st->castlingRights & (WHITE_OO | WHITE_OOO) << (2 * c);
}

inline bool Position::can_castle(int cr) const {
    return st->castlingRights & cr;
}

inline Bitboard Position::checkers() const {
    return st->checkersBB;
}

inline int Position::game_ply() const {
    return gamePly;
}

inline bool Position::is_chess960() const {
    return chess960;
}

inline uint64_t Position::key() const {
    return key_;
}

inline Value Position::psq_score() const {
    return VALUE_ZERO; // Will be implemented with piece-square tables
}

inline Value Position::non_pawn_material() const {
    return st->nonPawnMaterial[WHITE] + st->nonPawnMaterial[BLACK];
}

inline Value Position::non_pawn_material(Color c) const {
    return st->nonPawnMaterial[c];
}

#endif // POSITION_H

# Chess Engine Makefile

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -O3 -Wall -Wextra -march=native -DNDEBUG
DEBUGFLAGS = -std=c++17 -O0 -g -Wall -Wextra
LDFLAGS = -pthread

# Directories
SRCDIR = src
OBJDIR = obj
BINDIR = bin

# Source files
SOURCES = $(wildcard $(SRCDIR)/*.cpp)
OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(OBJDIR)/%.o)
TARGET = $(BINDIR)/ChessEngine

# Default target
all: $(TARGET)

# Create directories
$(OBJDIR):
	mkdir -p $(OBJDIR)

$(BINDIR):
	mkdir -p $(BINDIR)

# Build target
$(TARGET): $(OBJECTS) | $(BINDIR)
	$(CXX) $(OBJECTS) -o $@ $(LDFLAGS)

# Compile source files
$(OBJDIR)/%.o: $(SRCDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) -c $< -o $@

# Debug build
debug: CXXFLAGS = $(DEBUGFLAGS)
debug: $(TARGET)

# Clean build files
clean:
	rm -rf $(OBJDIR) $(BINDIR)

# Install
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

# Test targets
test: $(TARGET)
	./$(TARGET) test

bench: $(TARGET)
	./$(TARGET) bench

perft: $(TARGET)
	./$(TARGET) perft 6

# Format code
format:
	clang-format -i $(SRCDIR)/*.cpp $(SRCDIR)/*.h

# Static analysis
analyze:
	cppcheck --enable=all --std=c++17 $(SRCDIR)/

# Dependencies
-include $(OBJECTS:.o=.d)

$(OBJDIR)/%.d: $(SRCDIR)/%.cpp | $(OBJDIR)
	$(CXX) $(CXXFLAGS) -MM -MT $(@:.d=.o) $< > $@

.PHONY: all debug clean install test bench perft format analyze
